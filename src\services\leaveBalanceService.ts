import { supabase } from '@/integrations/supabase/client';
import { LeaveBalance } from '@/types/schedule';

export interface LeaveAccrualPolicy {
  annualLeavePerYear: number;
  sickLeavePerYear: number;
  emergencyLeavePerYear: number;
  maternityLeaveTotal: number;
  paternityLeaveTotal: number;
  carryOverLimit: number;
  carryOverExpiry: number; // months
}

export class LeaveBalanceService {
  
  // Default leave policy (can be customized per employee or department)
  private defaultPolicy: LeaveAccrualPolicy = {
    annualLeavePerYear: 12,
    sickLeavePerYear: 12,
    emergencyLeavePerYear: 3,
    maternityLeaveTotal: 90,
    paternityLeaveTotal: 3,
    carryOverLimit: 6,
    carryOverExpiry: 3,
  };

  /**
   * Initialize leave balance for a new employee
   */
  async initializeEmployeeBalance(
    employeeId: string,
    joinDate: string,
    year?: number
  ): Promise<boolean> {
    try {
      const targetYear = year || new Date().getFullYear();
      const joinDateObj = new Date(joinDate);
      const yearStart = new Date(targetYear, 0, 1);
      
      // Calculate prorated annual leave if joined mid-year
      let annualLeaveTotal = this.defaultPolicy.annualLeavePerYear;
      if (joinDateObj.getFullYear() === targetYear && joinDateObj > yearStart) {
        const monthsRemaining = 12 - joinDateObj.getMonth();
        annualLeaveTotal = Math.floor((monthsRemaining / 12) * this.defaultPolicy.annualLeavePerYear);
      }

      // Check if balance already exists
      const { data: existingBalance } = await supabase
        .from('leave_balances')
        .select('id')
        .eq('employee_id', employeeId)
        .eq('year', targetYear)
        .single();

      if (existingBalance) {
        return true; // Balance already exists
      }

      // Create new balance record
      const { error } = await supabase
        .from('leave_balances')
        .insert({
          employee_id: employeeId,
          year: targetYear,
          annual_leave_total: annualLeaveTotal,
          annual_leave_used: 0,
          annual_leave_remaining: annualLeaveTotal,
          sick_leave_used: 0,
          emergency_leave_used: 0,
          maternity_leave_used: 0,
        });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error initializing employee balance:', error);
      return false;
    }
  }

  /**
   * Update leave balance when leave is approved
   */
  async updateBalanceForApprovedLeave(
    employeeId: string,
    leaveType: string,
    startDate: string,
    endDate: string
  ): Promise<boolean> {
    try {
      const year = new Date(startDate).getFullYear();
      const leaveDays = this.calculateLeaveDays(startDate, endDate);

      // Get current balance
      const { data: balance, error: fetchError } = await supabase
        .from('leave_balances')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('year', year)
        .single();

      if (fetchError || !balance) {
        // Initialize balance if it doesn't exist
        await this.initializeEmployeeBalance(employeeId, startDate, year);
        return this.updateBalanceForApprovedLeave(employeeId, leaveType, startDate, endDate);
      }

      // Calculate new balance based on leave type
      const updates: Partial<LeaveBalance> = {};
      
      switch (leaveType.toLowerCase()) {
        case 'annual':
        case 'cuti tahunan':
          updates.annualLeaveUsed = balance.annualLeaveUsed + leaveDays;
          updates.annualLeaveRemaining = balance.annualLeaveRemaining - leaveDays;
          break;
        case 'sick':
        case 'cuti sakit':
          updates.sickLeaveUsed = balance.sickLeaveUsed + leaveDays;
          break;
        case 'emergency':
        case 'cuti darurat':
          updates.emergencyLeaveUsed = balance.emergencyLeaveUsed + leaveDays;
          break;
        case 'maternity':
        case 'cuti melahirkan':
          updates.maternityLeaveUsed = balance.maternityLeaveUsed + leaveDays;
          break;
        default:
          // For other leave types, deduct from annual leave
          updates.annualLeaveUsed = balance.annualLeaveUsed + leaveDays;
          updates.annualLeaveRemaining = balance.annualLeaveRemaining - leaveDays;
          break;
      }

      // Update the balance
      const { error: updateError } = await supabase
        .from('leave_balances')
        .update(updates)
        .eq('id', balance.id);

      if (updateError) throw updateError;
      return true;
    } catch (error) {
      console.error('Error updating balance for approved leave:', error);
      return false;
    }
  }

  /**
   * Reverse leave balance when leave is cancelled
   */
  async reverseBalanceForCancelledLeave(
    employeeId: string,
    leaveType: string,
    startDate: string,
    endDate: string
  ): Promise<boolean> {
    try {
      const year = new Date(startDate).getFullYear();
      const leaveDays = this.calculateLeaveDays(startDate, endDate);

      // Get current balance
      const { data: balance, error: fetchError } = await supabase
        .from('leave_balances')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('year', year)
        .single();

      if (fetchError || !balance) {
        return false;
      }

      // Calculate reversed balance based on leave type
      const updates: Partial<LeaveBalance> = {};
      
      switch (leaveType.toLowerCase()) {
        case 'annual':
        case 'cuti tahunan':
          updates.annualLeaveUsed = Math.max(0, balance.annualLeaveUsed - leaveDays);
          updates.annualLeaveRemaining = balance.annualLeaveRemaining + leaveDays;
          break;
        case 'sick':
        case 'cuti sakit':
          updates.sickLeaveUsed = Math.max(0, balance.sickLeaveUsed - leaveDays);
          break;
        case 'emergency':
        case 'cuti darurat':
          updates.emergencyLeaveUsed = Math.max(0, balance.emergencyLeaveUsed - leaveDays);
          break;
        case 'maternity':
        case 'cuti melahirkan':
          updates.maternityLeaveUsed = Math.max(0, balance.maternityLeaveUsed - leaveDays);
          break;
        default:
          updates.annualLeaveUsed = Math.max(0, balance.annualLeaveUsed - leaveDays);
          updates.annualLeaveRemaining = balance.annualLeaveRemaining + leaveDays;
          break;
      }

      // Update the balance
      const { error: updateError } = await supabase
        .from('leave_balances')
        .update(updates)
        .eq('id', balance.id);

      if (updateError) throw updateError;
      return true;
    } catch (error) {
      console.error('Error reversing balance for cancelled leave:', error);
      return false;
    }
  }

  /**
   * Process year-end carry over
   */
  async processYearEndCarryOver(year: number): Promise<boolean> {
    try {
      // Get all balances for the year
      const { data: balances, error: fetchError } = await supabase
        .from('leave_balances')
        .select(`
          *,
          employee:employees!employee_id (
            id,
            join_date,
            status
          )
        `)
        .eq('year', year);

      if (fetchError) throw fetchError;

      const nextYear = year + 1;
      const carryOverPromises = balances?.map(async (balance) => {
        if (!balance.employee || balance.employee.status !== 'active') {
          return;
        }

        // Calculate carry over amount (limited by policy)
        const carryOverDays = Math.min(
          balance.annualLeaveRemaining,
          this.defaultPolicy.carryOverLimit
        );

        // Initialize next year's balance
        const nextYearTotal = this.defaultPolicy.annualLeavePerYear + carryOverDays;
        
        await supabase
          .from('leave_balances')
          .upsert({
            employee_id: balance.employeeId,
            year: nextYear,
            annual_leave_total: nextYearTotal,
            annual_leave_used: 0,
            annual_leave_remaining: nextYearTotal,
            sick_leave_used: 0,
            emergency_leave_used: 0,
            maternity_leave_used: 0,
          }, {
            onConflict: 'employee_id,year'
          });
      }) || [];

      await Promise.all(carryOverPromises);
      return true;
    } catch (error) {
      console.error('Error processing year-end carry over:', error);
      return false;
    }
  }

  /**
   * Generate leave usage report
   */
  async generateUsageReport(
    year: number,
    departmentFilter?: string
  ): Promise<any[]> {
    try {
      let query = supabase
        .from('leave_balances')
        .select(`
          *,
          employee:employees!employee_id (
            first_name,
            last_name,
            department,
            position,
            join_date
          )
        `)
        .eq('year', year);

      if (departmentFilter) {
        query = query.eq('employee.department', departmentFilter);
      }

      const { data: balances, error } = await query;
      if (error) throw error;

      // Calculate statistics
      const report = balances?.map(balance => ({
        employeeName: `${balance.employee?.firstName} ${balance.employee?.lastName}`,
        department: balance.employee?.department,
        position: balance.employee?.position,
        joinDate: balance.employee?.joinDate,
        annualLeaveTotal: balance.annualLeaveTotal,
        annualLeaveUsed: balance.annualLeaveUsed,
        annualLeaveRemaining: balance.annualLeaveRemaining,
        sickLeaveUsed: balance.sickLeaveUsed,
        emergencyLeaveUsed: balance.emergencyLeaveUsed,
        maternityLeaveUsed: balance.maternityLeaveUsed,
        usagePercentage: balance.annualLeaveTotal > 0 
          ? ((balance.annualLeaveUsed / balance.annualLeaveTotal) * 100).toFixed(1)
          : '0',
        status: this.getBalanceStatus(balance),
      })) || [];

      return report;
    } catch (error) {
      console.error('Error generating usage report:', error);
      return [];
    }
  }

  /**
   * Calculate number of leave days between two dates
   */
  private calculateLeaveDays(startDate: string, endDate: string): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays + 1; // Include both start and end dates
  }

  /**
   * Get balance status for reporting
   */
  private getBalanceStatus(balance: LeaveBalance): string {
    if (balance.annualLeaveRemaining <= 0) return 'Exhausted';
    if (balance.annualLeaveRemaining <= 2) return 'Low';
    if (balance.annualLeaveRemaining >= balance.annualLeaveTotal * 0.8) return 'High';
    return 'Normal';
  }

  /**
   * Check if employee has sufficient leave balance
   */
  async checkLeaveAvailability(
    employeeId: string,
    leaveType: string,
    startDate: string,
    endDate: string
  ): Promise<{ available: boolean; message: string; remainingDays?: number }> {
    try {
      const year = new Date(startDate).getFullYear();
      const requestedDays = this.calculateLeaveDays(startDate, endDate);

      const { data: balance, error } = await supabase
        .from('leave_balances')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('year', year)
        .single();

      if (error || !balance) {
        return {
          available: false,
          message: 'Leave balance not found. Please contact HR.',
        };
      }

      switch (leaveType.toLowerCase()) {
        case 'annual':
        case 'cuti tahunan':
          if (balance.annualLeaveRemaining >= requestedDays) {
            return {
              available: true,
              message: 'Leave request can be approved.',
              remainingDays: balance.annualLeaveRemaining - requestedDays,
            };
          } else {
            return {
              available: false,
              message: `Insufficient annual leave balance. You have ${balance.annualLeaveRemaining} days remaining, but requested ${requestedDays} days.`,
              remainingDays: balance.annualLeaveRemaining,
            };
          }
        
        case 'sick':
        case 'emergency':
        case 'maternity':
          // These leave types typically don't have strict limits
          return {
            available: true,
            message: 'Leave request can be approved.',
          };
        
        default:
          // For other leave types, check against annual leave
          if (balance.annualLeaveRemaining >= requestedDays) {
            return {
              available: true,
              message: 'Leave request can be approved (deducted from annual leave).',
              remainingDays: balance.annualLeaveRemaining - requestedDays,
            };
          } else {
            return {
              available: false,
              message: `Insufficient leave balance. You have ${balance.annualLeaveRemaining} days remaining, but requested ${requestedDays} days.`,
              remainingDays: balance.annualLeaveRemaining,
            };
          }
      }
    } catch (error) {
      console.error('Error checking leave availability:', error);
      return {
        available: false,
        message: 'Error checking leave availability. Please try again.',
      };
    }
  }
}

export const leaveBalanceService = new LeaveBalanceService();
