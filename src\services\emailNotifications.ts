import { supabase } from '@/integrations/supabase/client';

export interface EmailNotificationData {
  to: string[];
  subject: string;
  htmlContent: string;
  textContent?: string;
  templateData?: Record<string, any>;
}

export class EmailNotificationService {
  
  /**
   * Send email notification using Supabase Edge Functions
   */
  async sendEmail(data: EmailNotificationData): Promise<boolean> {
    try {
      const { data: result, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: data.to,
          subject: data.subject,
          html: data.htmlContent,
          text: data.textContent,
        },
      });

      if (error) {
        console.error('Email sending error:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Email service error:', error);
      return false;
    }
  }

  /**
   * Send schedule change notification email
   */
  async sendScheduleChangeEmail(
    employeeEmails: string[],
    employeeName: string,
    scheduleDate: string,
    changeType: 'created' | 'updated' | 'cancelled' | 'replaced',
    scheduleDetails: {
      shiftType: string;
      startTime: string;
      endTime: string;
      department: string;
    },
    additionalInfo?: string
  ): Promise<boolean> {
    const changeTypeLabels = {
      created: 'New Schedule Assigned',
      updated: 'Schedule Updated',
      cancelled: 'Schedule Cancelled',
      replaced: 'Schedule Replaced',
    };

    const subject = `${changeTypeLabels[changeType]} - ${new Date(scheduleDate).toLocaleDateString('id-ID')}`;
    
    const htmlContent = this.generateScheduleChangeEmailTemplate(
      employeeName,
      scheduleDate,
      changeType,
      scheduleDetails,
      additionalInfo
    );

    const textContent = this.generateScheduleChangeTextContent(
      employeeName,
      scheduleDate,
      changeType,
      scheduleDetails,
      additionalInfo
    );

    return await this.sendEmail({
      to: employeeEmails,
      subject,
      htmlContent,
      textContent,
    });
  }

  /**
   * Send leave request notification email
   */
  async sendLeaveRequestEmail(
    approverEmails: string[],
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    reason?: string
  ): Promise<boolean> {
    const subject = `Leave Request - ${employeeName}`;
    
    const htmlContent = this.generateLeaveRequestEmailTemplate(
      employeeName,
      leaveType,
      startDate,
      endDate,
      reason
    );

    const textContent = this.generateLeaveRequestTextContent(
      employeeName,
      leaveType,
      startDate,
      endDate,
      reason
    );

    return await this.sendEmail({
      to: approverEmails,
      subject,
      htmlContent,
      textContent,
    });
  }

  /**
   * Send leave approval/rejection notification email
   */
  async sendLeaveResponseEmail(
    employeeEmail: string,
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    status: 'approved' | 'rejected',
    comments?: string
  ): Promise<boolean> {
    const subject = `Leave Request ${status === 'approved' ? 'Approved' : 'Rejected'}`;
    
    const htmlContent = this.generateLeaveResponseEmailTemplate(
      employeeName,
      leaveType,
      startDate,
      endDate,
      status,
      comments
    );

    const textContent = this.generateLeaveResponseTextContent(
      employeeName,
      leaveType,
      startDate,
      endDate,
      status,
      comments
    );

    return await this.sendEmail({
      to: [employeeEmail],
      subject,
      htmlContent,
      textContent,
    });
  }

  /**
   * Generate HTML template for schedule change emails
   */
  private generateScheduleChangeEmailTemplate(
    employeeName: string,
    scheduleDate: string,
    changeType: string,
    scheduleDetails: any,
    additionalInfo?: string
  ): string {
    const formattedDate = new Date(scheduleDate).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Schedule Notification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #3b82f6; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9fafb; }
          .schedule-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Schedule Notification</h1>
          </div>
          <div class="content">
            <p>Dear ${employeeName},</p>
            <p>This is to inform you that your schedule has been ${changeType}.</p>
            
            <div class="schedule-details">
              <h3>Schedule Details:</h3>
              <p><strong>Date:</strong> ${formattedDate}</p>
              <p><strong>Shift:</strong> ${scheduleDetails.shiftType}</p>
              <p><strong>Time:</strong> ${scheduleDetails.startTime} - ${scheduleDetails.endTime}</p>
              <p><strong>Department:</strong> ${scheduleDetails.department}</p>
            </div>
            
            ${additionalInfo ? `<p><strong>Additional Information:</strong> ${additionalInfo}</p>` : ''}
            
            <p>Please log in to the system to view your updated schedule.</p>
            <p>If you have any questions, please contact your supervisor or HR department.</p>
          </div>
          <div class="footer">
            <p>This is an automated message from the Hospital Employee Management System.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate text content for schedule change emails
   */
  private generateScheduleChangeTextContent(
    employeeName: string,
    scheduleDate: string,
    changeType: string,
    scheduleDetails: any,
    additionalInfo?: string
  ): string {
    const formattedDate = new Date(scheduleDate).toLocaleDateString('id-ID');
    
    return `
Dear ${employeeName},

This is to inform you that your schedule has been ${changeType}.

Schedule Details:
- Date: ${formattedDate}
- Shift: ${scheduleDetails.shiftType}
- Time: ${scheduleDetails.startTime} - ${scheduleDetails.endTime}
- Department: ${scheduleDetails.department}

${additionalInfo ? `Additional Information: ${additionalInfo}` : ''}

Please log in to the system to view your updated schedule.
If you have any questions, please contact your supervisor or HR department.

This is an automated message from the Hospital Employee Management System.
    `.trim();
  }

  /**
   * Generate HTML template for leave request emails
   */
  private generateLeaveRequestEmailTemplate(
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    reason?: string
  ): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Leave Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #10b981; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9fafb; }
          .leave-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Leave Request</h1>
          </div>
          <div class="content">
            <p>A new leave request has been submitted and requires your approval.</p>
            
            <div class="leave-details">
              <h3>Leave Request Details:</h3>
              <p><strong>Employee:</strong> ${employeeName}</p>
              <p><strong>Leave Type:</strong> ${leaveType}</p>
              <p><strong>Start Date:</strong> ${new Date(startDate).toLocaleDateString('id-ID')}</p>
              <p><strong>End Date:</strong> ${new Date(endDate).toLocaleDateString('id-ID')}</p>
              ${reason ? `<p><strong>Reason:</strong> ${reason}</p>` : ''}
            </div>
            
            <p>Please log in to the system to review and approve/reject this request.</p>
          </div>
          <div class="footer">
            <p>This is an automated message from the Hospital Employee Management System.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate text content for leave request emails
   */
  private generateLeaveRequestTextContent(
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    reason?: string
  ): string {
    return `
A new leave request has been submitted and requires your approval.

Leave Request Details:
- Employee: ${employeeName}
- Leave Type: ${leaveType}
- Start Date: ${new Date(startDate).toLocaleDateString('id-ID')}
- End Date: ${new Date(endDate).toLocaleDateString('id-ID')}
${reason ? `- Reason: ${reason}` : ''}

Please log in to the system to review and approve/reject this request.

This is an automated message from the Hospital Employee Management System.
    `.trim();
  }

  /**
   * Generate HTML template for leave response emails
   */
  private generateLeaveResponseEmailTemplate(
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    status: 'approved' | 'rejected',
    comments?: string
  ): string {
    const statusColor = status === 'approved' ? '#10b981' : '#ef4444';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Leave Request ${status === 'approved' ? 'Approved' : 'Rejected'}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: ${statusColor}; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9fafb; }
          .leave-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Leave Request ${status === 'approved' ? 'Approved' : 'Rejected'}</h1>
          </div>
          <div class="content">
            <p>Dear ${employeeName},</p>
            <p>Your leave request has been ${status}.</p>
            
            <div class="leave-details">
              <h3>Leave Details:</h3>
              <p><strong>Leave Type:</strong> ${leaveType}</p>
              <p><strong>Start Date:</strong> ${new Date(startDate).toLocaleDateString('id-ID')}</p>
              <p><strong>End Date:</strong> ${new Date(endDate).toLocaleDateString('id-ID')}</p>
              <p><strong>Status:</strong> ${status.toUpperCase()}</p>
              ${comments ? `<p><strong>Comments:</strong> ${comments}</p>` : ''}
            </div>
            
            <p>Please log in to the system to view the details.</p>
          </div>
          <div class="footer">
            <p>This is an automated message from the Hospital Employee Management System.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate text content for leave response emails
   */
  private generateLeaveResponseTextContent(
    employeeName: string,
    leaveType: string,
    startDate: string,
    endDate: string,
    status: 'approved' | 'rejected',
    comments?: string
  ): string {
    return `
Dear ${employeeName},

Your leave request has been ${status}.

Leave Details:
- Leave Type: ${leaveType}
- Start Date: ${new Date(startDate).toLocaleDateString('id-ID')}
- End Date: ${new Date(endDate).toLocaleDateString('id-ID')}
- Status: ${status.toUpperCase()}
${comments ? `- Comments: ${comments}` : ''}

Please log in to the system to view the details.

This is an automated message from the Hospital Employee Management System.
    `.trim();
  }
}

export const emailNotificationService = new EmailNotificationService();
