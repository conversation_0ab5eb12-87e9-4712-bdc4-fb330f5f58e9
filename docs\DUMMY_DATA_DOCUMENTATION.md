# Comprehensive Dummy Data Documentation
## Employee Management System (Manajemen Karyawan RS)

This document provides detailed information about the comprehensive dummy data created for testing the Employee Management System, with a focus on Schedule Management (Manajemen Jadwal) and Leave and Permit Management (Cuti dan I<PERSON>) modules.

## Overview

The dummy data has been designed to simulate realistic hospital operations following Indonesian workplace standards and regulations. It includes various scenarios for testing all features of both modules.

## Data Structure

### 1. Enhanced Employee Data

**Total Employees**: 16 employees across various departments and roles

**Departments Covered**:
- Emergency (Gawat Darurat)
- Surgery (Bedah)
- Pediatrics (Anak)
- Cardiology (Jantung)
- Orthopedics (Ortopedi)
- Pharmacy (Farmasi)
- Laboratory (Laboratorium)
- Radiology (Radiologi)
- Administration (Administrasi)

**Roles Included**:
- Doctors (Dokter): 5 employees
- Nurses (Perawat): 5 employees
- Pharmacists (Apoteker): 2 employees
- Technicians (Teknisi): 2 employees
- Receptionist (Resepsionis): 1 employee
- Manager (Manajer): 1 employee

### 2. Schedule Management Data

#### Schedules Table
- **Total Records**: ~1,500+ schedule entries
- **Date Range**: 30 days in the past to 90 days in the future
- **Shift Types**:
  - Morning (Pagi): 07:00 - 15:00
  - Afternoon (Siang): 15:00 - 23:00
  - Night (Malam): 23:00 - 07:00
  - Rotating (Bergilir): Dynamic assignment

#### Shift Patterns
- **Emergency Department**: 24/7 coverage with rotating shifts
- **Regular Departments**: Standard working hours with weekend variations
- **Realistic Status Distribution**:
  - Completed: 85% (for past dates)
  - Scheduled: 95% (for current/future dates)
  - No Show: 5% (realistic absence rate)
  - Cancelled: Minimal occurrences

#### Schedule Replacements
- **Total Records**: ~75+ replacement requests
- **Scenarios Covered**:
  - Sudden illness (40%)
  - Family emergencies (30%)
  - Personal schedule conflicts (30%)
- **Status Distribution**:
  - Completed: 80% (for past dates)
  - Approved: 60% (for future dates)
  - Pending: 40% (for future dates)

### 3. Leave and Permit Management Data

#### Leave Requests
- **Total Records**: ~150+ leave requests
- **Leave Types** (Following Indonesian Standards):
  - Cuti Tahunan (Annual Leave): 12-15 days per year
  - Cuti Sakit (Sick Leave): With medical certificates
  - Cuti Melahirkan (Maternity Leave): 90 days
  - Cuti Menikah (Marriage Leave): 3 days
  - Cuti Duka Cita (Bereavement Leave): 2-3 days
  - Cuti Haji/Umroh (Pilgrimage Leave): 14-40 days
  - Cuti Darurat Keluarga (Emergency Family Leave): 1-2 days
  - Cuti Pendidikan (Education Leave): 5-30 days

#### Status Distribution
- **Approved**: 70% of historical requests
- **Rejected**: 20% of historical requests
- **Pending**: 10% of current/future requests

#### Leave Balances
- **Coverage**: All active employees for current and previous year
- **Realistic Usage Patterns**:
  - Annual leave: 0-7 days used (current year)
  - Sick leave: 0-4 days used
  - Emergency leave: 0-2 days used
  - Maternity leave: Applicable to female nurses (30% probability)

#### Medical Certificates
- **Total Records**: ~30+ certificates for sick leave
- **Realistic Data**:
  - Doctor names and specializations
  - Hospital/clinic names (Indonesian healthcare providers)
  - Common diagnoses (flu, gastroenteritis, migraine)
  - Certificate numbers and verification status
  - File path references for uploaded documents

#### Leave Approval Workflow
- **Multi-level Approval System**:
  - Level 1: Direct supervisor/department manager
  - Level 2: HR department (for extended leave)
- **Approval Comments**: Realistic Indonesian workplace responses
- **Timestamp Tracking**: Complete audit trail

### 4. Notification System

#### Notification Types
- **Leave Approved/Rejected**: Status updates for leave requests
- **Schedule Changes**: Shift modifications and updates
- **Replacement Requests**: Shift substitution notifications
- **Schedule Reminders**: Next-day work reminders
- **System Information**: Administrative notifications

#### Read Status Distribution
- **Read**: 60% of notifications (realistic user engagement)
- **Unread**: 40% of notifications

### 5. Additional Features

#### Weekend Emergency Coverage
- Special scheduling for emergency department
- 60% coverage rate for weekends
- Rotating staff assignments

#### Performance Optimization
- **Database Indexes**: Created for optimal query performance
- **Composite Indexes**: For complex filtering scenarios
- **Foreign Key Relationships**: Properly maintained data integrity

## Testing Scenarios Covered

### Schedule Management Testing
1. **Calendar View Testing**:
   - Multiple employees per day
   - Different shift types visualization
   - Weekend and holiday coverage

2. **Employee Schedule View**:
   - Individual employee schedules
   - Shift pattern analysis
   - Workload distribution

3. **Monthly Schedule View**:
   - Department-wide scheduling
   - Resource allocation
   - Coverage gaps identification

4. **Schedule Replacement**:
   - Emergency substitutions
   - Approval workflow
   - Notification system

### Leave Management Testing
1. **Leave Application Process**:
   - Various leave types
   - Date range validation
   - Reason documentation

2. **Approval Workflow**:
   - Multi-level approvals
   - Comments and feedback
   - Status tracking

3. **Leave Balance Tracking**:
   - Annual entitlement calculation
   - Usage monitoring
   - Remaining balance display

4. **Medical Certificate Management**:
   - Document upload simulation
   - Verification process
   - Compliance tracking

5. **Reporting and Analytics**:
   - Leave usage patterns
   - Department-wise analysis
   - Trend identification

## Indonesian Workplace Compliance

The dummy data follows Indonesian labor regulations:
- **Annual Leave**: 12 days minimum, 15 days for senior employees
- **Maternity Leave**: 90 days (3 months)
- **Marriage Leave**: 3 days
- **Bereavement Leave**: 2-3 days depending on relationship
- **Sick Leave**: Requires medical certificate for >2 days
- **Religious Leave**: Haji/Umroh provisions

## Usage Instructions

1. **Database Migration**: Run the migration file to populate all dummy data
2. **Testing Environment**: Use for development and testing purposes only
3. **Data Reset**: Can be safely removed and regenerated as needed
4. **Performance Testing**: Sufficient data volume for load testing

## Data Relationships

All dummy data maintains proper foreign key relationships:
- Employees ↔ Schedules
- Employees ↔ Leave Requests
- Leave Requests ↔ Leave Approvals
- Leave Requests ↔ Medical Certificates
- Schedules ↔ Schedule Replacements
- Employees ↔ Notifications
- Employees ↔ Leave Balances

This comprehensive dummy data set provides a realistic testing environment for all features of the Employee Management System, ensuring thorough validation of both Schedule Management and Leave Management modules.
