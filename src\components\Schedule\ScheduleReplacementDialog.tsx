import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Users, Calendar, Clock, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Schedule, ScheduleReplacement } from '@/types/schedule';
import { useEmployees } from '@/hooks/useEmployees';
import { useAuth } from '@/hooks/useAuth';

const replacementSchema = z.object({
  replacementEmployeeId: z.string().min(1, 'Replacement employee is required'),
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
});

type ReplacementFormData = z.infer<typeof replacementSchema>;

interface ScheduleReplacementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  schedule?: Schedule;
  onSuccess?: () => void;
}

export function ScheduleReplacementDialog({ 
  open, 
  onOpenChange, 
  schedule,
  onSuccess 
}: ScheduleReplacementDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [availableEmployees, setAvailableEmployees] = useState<any[]>([]);
  const { toast } = useToast();
  const { employees } = useEmployees();
  const { user } = useAuth();

  const form = useForm<ReplacementFormData>({
    resolver: zodResolver(replacementSchema),
    defaultValues: {
      replacementEmployeeId: '',
      reason: '',
      notes: '',
    },
  });

  // Find available employees for replacement
  useEffect(() => {
    if (open && schedule) {
      findAvailableEmployees();
    }
  }, [open, schedule]);

  const findAvailableEmployees = async () => {
    if (!schedule) return;

    try {
      // Get employees with same role/department who are not already scheduled
      const { data: conflictingSchedules, error: scheduleError } = await supabase
        .from('schedules')
        .select('employee_id')
        .eq('shift_date', schedule.shiftDate)
        .neq('id', schedule.id);

      if (scheduleError) throw scheduleError;

      const busyEmployeeIds = conflictingSchedules?.map(s => s.employee_id) || [];

      // Get employees on leave
      const { data: leaveRequests, error: leaveError } = await supabase
        .from('leave_requests')
        .select('employee_id')
        .eq('status', 'approved')
        .lte('start_date', schedule.shiftDate)
        .gte('end_date', schedule.shiftDate);

      if (leaveError) throw leaveError;

      const onLeaveEmployeeIds = leaveRequests?.map(l => l.employee_id) || [];

      // Filter available employees
      const available = employees.filter(emp => 
        emp.id !== schedule.employeeId && // Not the original employee
        !busyEmployeeIds.includes(emp.id) && // Not already scheduled
        !onLeaveEmployeeIds.includes(emp.id) && // Not on leave
        emp.status === 'active' && // Active employees only
        (emp.department === schedule.employee?.department || // Same department
         emp.role === schedule.employee?.position) // Or same role
      );

      setAvailableEmployees(available);
    } catch (error) {
      console.error('Error finding available employees:', error);
      toast({
        title: "Error",
        description: "Failed to load available employees",
        variant: "destructive",
      });
    }
  };

  const handleSubmitReplacement = async (data: ReplacementFormData) => {
    if (!schedule || !user) return;

    setIsLoading(true);
    try {
      // Get current user's employee record
      const { data: currentEmployee, error: empError } = await supabase
        .from('employees')
        .select('id')
        .eq('email', user.email)
        .single();

      if (empError) throw empError;

      const replacementData = {
        original_schedule_id: schedule.id,
        replacement_employee_id: data.replacementEmployeeId,
        reason: data.reason,
        status: 'pending',
        requested_by: currentEmployee.id,
        notes: data.notes || null,
      };

      const { error } = await supabase
        .from('schedule_replacements')
        .insert(replacementData);

      if (error) throw error;

      // Create notification for the replacement employee
      const replacementEmployee = employees.find(emp => emp.id === data.replacementEmployeeId);
      if (replacementEmployee) {
        await supabase
          .from('notifications')
          .insert({
            recipient_id: data.replacementEmployeeId,
            title: 'Schedule Replacement Request',
            message: `You have been requested to replace ${schedule.employee?.firstName} ${schedule.employee?.lastName} on ${new Date(schedule.shiftDate).toLocaleDateString('id-ID')} (${schedule.startTime} - ${schedule.endTime})`,
            type: 'replacement_request',
            related_id: schedule.id,
          });
      }

      toast({
        title: "Success",
        description: "Replacement request submitted successfully",
      });

      if (onSuccess) {
        onSuccess();
      }
      
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit replacement request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setAvailableEmployees([]);
    onOpenChange(false);
  };

  const reasonOptions = [
    'Sick Leave',
    'Emergency',
    'Personal Matter',
    'Family Emergency',
    'Medical Appointment',
    'Other',
  ];

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Request Schedule Replacement</span>
          </DialogTitle>
        </DialogHeader>

        {schedule && (
          <div className="space-y-6">
            {/* Original Schedule Info */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Original Schedule</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Employee:</span>
                  <span className="font-medium">
                    {schedule.employee?.firstName} {schedule.employee?.lastName}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Date:</span>
                  <span className="font-medium">
                    {new Date(schedule.shiftDate).toLocaleDateString('id-ID')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Time:</span>
                  <span className="font-medium">{schedule.startTime} - {schedule.endTime}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Shift Type:</span>
                  <Badge>{schedule.shiftType}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Department:</span>
                  <Badge variant="outline">{schedule.employee?.department}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Available Employees Alert */}
            {availableEmployees.length === 0 ? (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No available employees found for replacement. All eligible employees are either 
                  already scheduled, on leave, or inactive.
                </AlertDescription>
              </Alert>
            ) : (
              <Alert>
                <Users className="h-4 w-4" />
                <AlertDescription>
                  Found {availableEmployees.length} available employee(s) for replacement.
                </AlertDescription>
              </Alert>
            )}

            {/* Replacement Form */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmitReplacement)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="replacementEmployeeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Replacement Employee</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        value={field.value}
                        disabled={availableEmployees.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select replacement employee" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableEmployees.map((employee) => (
                            <SelectItem key={employee.id} value={employee.id}>
                              <div className="flex items-center justify-between w-full">
                                <span>{employee.firstName} {employee.lastName}</span>
                                <div className="flex space-x-1 ml-2">
                                  <Badge variant="outline" className="text-xs">
                                    {employee.department}
                                  </Badge>
                                  <Badge variant="secondary" className="text-xs">
                                    {employee.position}
                                  </Badge>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for Replacement</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select reason" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {reasonOptions.map((reason) => (
                            <SelectItem key={reason} value={reason}>
                              {reason}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Any additional information about the replacement request..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isLoading || availableEmployees.length === 0}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      'Submit Request'
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
