
import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ChevronLeft, ChevronRight, Calendar, Grid, Building } from 'lucide-react';
import { Schedule } from '@/types/schedule';
import { useManagement } from '@/hooks/useManagement';

interface MonthlyScheduleViewProps {
  schedules: Schedule[];
}

export function MonthlyScheduleView({ schedules }: MonthlyScheduleViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const { departments, isLoadingDepartments } = useManagement();

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const getMonthlySchedules = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const startDate = new Date(year, month, 1).toISOString().split('T')[0];
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];
    
    let filteredSchedules = schedules.filter(schedule => 
      schedule.shiftDate >= startDate && schedule.shiftDate <= endDate
    );

    if (selectedDepartment !== 'all') {
      filteredSchedules = filteredSchedules.filter(schedule =>
        schedule.employee?.department === selectedDepartment
      );
    }

    return filteredSchedules;
  };

  const getSchedulesForDate = (date: Date | null) => {
    if (!date) return [];
    const dateString = date.toISOString().split('T')[0];
    return getMonthlySchedules().filter(schedule => schedule.shiftDate === dateString);
  };

  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1));
  };

  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-500';
      case 'afternoon': return 'bg-blue-500';
      case 'night': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getShiftTypeBadgeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const days = getDaysInMonth(currentDate);
  const monthlySchedules = getMonthlySchedules();

  // Group schedules by employee for table view
  const employeeSchedules = monthlySchedules.reduce((acc, schedule) => {
    const key = `${schedule.employee?.firstName} ${schedule.employee?.lastName}`;
    if (!acc[key]) {
      acc[key] = {
        employee: schedule.employee,
        schedules: [],
      };
    }
    acc[key].schedules.push(schedule);
    return acc;
  }, {} as Record<string, { employee: any; schedules: Schedule[] }>);

  if (isLoadingDepartments) {
    return <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building className="h-5 w-5" />
          <span>Jadwal Bulanan per Departemen</span>
        </CardTitle>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h3 className="text-lg font-semibold min-w-[150px] text-center">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h3>
            <Button variant="outline" size="sm" onClick={goToNextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Pilih departemen" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Departemen</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.name}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="calendar" className="space-y-4">
          <TabsList>
            <TabsTrigger value="calendar" className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Kalender</span>
            </TabsTrigger>
            <TabsTrigger value="table" className="flex items-center space-x-2">
              <Grid className="h-4 w-4" />
              <span>Tabel</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="calendar">
            <div className="space-y-4">
              <div className="grid grid-cols-7 gap-1 mb-4">
                {dayNames.map((day) => (
                  <div key={day} className="p-2 text-center font-medium text-gray-600 text-sm">
                    {day}
                  </div>
                ))}
              </div>
              
              <div className="grid grid-cols-7 gap-1">
                {days.map((day, index) => {
                  const daySchedules = getSchedulesForDate(day);
                  const isToday = day && day.toDateString() === new Date().toDateString();
                  
                  return (
                    <div
                      key={index}
                      className={`min-h-[100px] p-2 border rounded ${
                        day ? 'bg-white' : 'bg-gray-50'
                      } ${isToday ? 'ring-2 ring-blue-500' : ''}`}
                    >
                      {day && (
                        <>
                          <div className={`text-sm font-medium mb-2 ${
                            isToday ? 'text-blue-600' : 'text-gray-900'
                          }`}>
                            {day.getDate()}
                          </div>
                          <div className="space-y-1">
                            {daySchedules.slice(0, 3).map((schedule, idx) => (
                              <div
                                key={idx}
                                className="text-xs p-1 rounded bg-gray-100 truncate"
                                title={`${schedule.employee?.firstName} ${schedule.employee?.lastName} - ${schedule.shiftType}`}
                              >
                                <div className={`w-2 h-2 rounded-full inline-block mr-1 ${getShiftTypeColor(schedule.shiftType)}`} />
                                {schedule.employee?.firstName} {schedule.employee?.lastName}
                              </div>
                            ))}
                            {daySchedules.length > 3 && (
                              <div className="text-xs text-gray-500">
                                +{daySchedules.length - 3} lagi
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="table">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Karyawan</TableHead>
                  <TableHead>Departemen</TableHead>
                  <TableHead>Total Jadwal</TableHead>
                  <TableHead>Shift Pagi</TableHead>
                  <TableHead>Shift Siang</TableHead>
                  <TableHead>Shift Malam</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Object.entries(employeeSchedules).map(([employeeName, data]) => {
                  const morningCount = data.schedules.filter(s => s.shiftType === 'morning').length;
                  const afternoonCount = data.schedules.filter(s => s.shiftType === 'afternoon').length;
                  const nightCount = data.schedules.filter(s => s.shiftType === 'night').length;
                  
                  return (
                    <TableRow key={employeeName}>
                      <TableCell className="font-medium">{employeeName}</TableCell>
                      <TableCell>{data.employee?.department}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {data.schedules.length} jadwal
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={morningCount > 0 ? getShiftTypeBadgeColor('morning') : 'bg-gray-100 text-gray-400'}>
                          {morningCount}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={afternoonCount > 0 ? getShiftTypeBadgeColor('afternoon') : 'bg-gray-100 text-gray-400'}>
                          {afternoonCount}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={nightCount > 0 ? getShiftTypeBadgeColor('night') : 'bg-gray-100 text-gray-400'}>
                          {nightCount}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
            
            {Object.keys(employeeSchedules).length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Tidak ada jadwal pada bulan ini untuk departemen yang dipilih</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
