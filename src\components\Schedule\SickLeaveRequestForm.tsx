import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { 
  Heart, 
  Calendar, 
  FileText, 
  AlertTriangle, 
  CheckCircle,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { MedicalCertificateUpload } from './MedicalCertificateUpload';
import { useAuth } from '@/hooks/useAuth';
import { leaveBalanceService } from '@/services/leaveBalanceService';

const sickLeaveSchema = z.object({
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  symptoms: z.string().min(1, 'Please describe your symptoms'),
  emergencyContact: z.string().min(1, 'Emergency contact is required'),
  emergencyPhone: z.string().min(1, 'Emergency phone number is required'),
  requiresHospitalization: z.boolean().default(false),
  isContagious: z.boolean().default(false),
  returnToWorkDate: z.string().optional(),
  additionalNotes: z.string().optional(),
}).refine((data) => {
  const start = new Date(data.startDate);
  const end = new Date(data.endDate);
  return end >= start;
}, {
  message: "End date must be after or equal to start date",
  path: ["endDate"],
});

type SickLeaveFormData = z.infer<typeof sickLeaveSchema>;

interface SickLeaveRequestFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function SickLeaveRequestForm({ onSuccess, onCancel }: SickLeaveRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [leaveRequestId, setLeaveRequestId] = useState<string | null>(null);
  const [showCertificateUpload, setShowCertificateUpload] = useState(false);
  const [balanceCheck, setBalanceCheck] = useState<any>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const form = useForm<SickLeaveFormData>({
    resolver: zodResolver(sickLeaveSchema),
    defaultValues: {
      startDate: '',
      endDate: '',
      symptoms: '',
      emergencyContact: '',
      emergencyPhone: '',
      requiresHospitalization: false,
      isContagious: false,
      returnToWorkDate: '',
      additionalNotes: '',
    },
  });

  const watchedDates = form.watch(['startDate', 'endDate']);

  // Check leave balance when dates change
  React.useEffect(() => {
    const checkBalance = async () => {
      if (watchedDates[0] && watchedDates[1] && user?.email) {
        try {
          const { data: employee } = await supabase
            .from('employees')
            .select('id')
            .eq('email', user.email)
            .single();

          if (employee) {
            const result = await leaveBalanceService.checkLeaveAvailability(
              employee.id,
              'sick',
              watchedDates[0],
              watchedDates[1]
            );
            setBalanceCheck(result);
          }
        } catch (error) {
          console.error('Error checking balance:', error);
        }
      }
    };

    checkBalance();
  }, [watchedDates, user]);

  const calculateLeaveDays = (startDate: string, endDate: string): number => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays + 1;
  };

  const handleSubmit = async (data: SickLeaveFormData) => {
    if (!user?.email) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Get current user's employee record
      const { data: employee, error: empError } = await supabase
        .from('employees')
        .select('id, first_name, last_name')
        .eq('email', user.email)
        .single();

      if (empError || !employee) {
        throw new Error('Employee record not found');
      }

      const leaveDays = calculateLeaveDays(data.startDate, data.endDate);

      // Create sick leave request with additional sick leave specific fields
      const leaveRequestData = {
        employee_id: employee.id,
        leave_type: 'sick',
        start_date: data.startDate,
        end_date: data.endDate,
        reason: `Sick Leave - Symptoms: ${data.symptoms}`,
        status: 'pending',
        // Store sick leave specific data in notes field as JSON
        notes: JSON.stringify({
          symptoms: data.symptoms,
          emergencyContact: data.emergencyContact,
          emergencyPhone: data.emergencyPhone,
          requiresHospitalization: data.requiresHospitalization,
          isContagious: data.isContagious,
          returnToWorkDate: data.returnToWorkDate,
          additionalNotes: data.additionalNotes,
          totalDays: leaveDays,
          submissionDate: new Date().toISOString(),
        }),
      };

      const { data: leaveRequest, error: leaveError } = await supabase
        .from('leave_requests')
        .insert(leaveRequestData)
        .select()
        .single();

      if (leaveError) throw leaveError;

      setLeaveRequestId(leaveRequest.id);

      // Create initial approval workflow
      const { data: supervisor } = await supabase
        .from('employees')
        .select('id')
        .eq('department', employee.department)
        .eq('role', 'manager')
        .limit(1)
        .single();

      if (supervisor) {
        await supabase
          .from('leave_approvals')
          .insert({
            leave_request_id: leaveRequest.id,
            approver_id: supervisor.id,
            approval_level: 1,
            status: 'pending',
          });
      }

      // Send notification to supervisor
      if (supervisor) {
        await supabase
          .from('notifications')
          .insert({
            recipient_id: supervisor.id,
            title: 'Sick Leave Request Requires Approval',
            message: `${employee.first_name} ${employee.last_name} has submitted a sick leave request from ${new Date(data.startDate).toLocaleDateString('id-ID')} to ${new Date(data.endDate).toLocaleDateString('id-ID')}. Medical certificate ${leaveDays > 3 ? 'is required' : 'may be required'}.`,
            type: 'leave_request',
            related_id: leaveRequest.id,
          });
      }

      toast({
        title: "Success",
        description: "Sick leave request submitted successfully",
      });

      // Show certificate upload if leave is more than 3 days
      if (leaveDays > 3) {
        setShowCertificateUpload(true);
      } else {
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error submitting sick leave request:', error);
      toast({
        title: "Error",
        description: "Failed to submit sick leave request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCertificateUploadSuccess = () => {
    toast({
      title: "Complete",
      description: "Sick leave request and medical certificate submitted successfully",
    });
    if (onSuccess) onSuccess();
  };

  const leaveDays = watchedDates[0] && watchedDates[1] 
    ? calculateLeaveDays(watchedDates[0], watchedDates[1]) 
    : 0;

  if (showCertificateUpload && leaveRequestId) {
    return (
      <div className="space-y-6">
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Your sick leave request has been submitted successfully. Since your leave is more than 3 days, 
            please upload a medical certificate to complete your request.
          </AlertDescription>
        </Alert>
        
        <MedicalCertificateUpload
          leaveRequestId={leaveRequestId}
          onUploadSuccess={handleCertificateUploadSuccess}
        />
        
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => {
            if (onSuccess) onSuccess();
          }}>
            Skip for Now
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Heart className="h-5 w-5 text-red-500" />
          <span>Sick Leave Request</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Leave Period */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Leave Period</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {leaveDays > 0 && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">
                      Total Leave Days: {leaveDays} day{leaveDays > 1 ? 's' : ''}
                    </span>
                  </div>
                  {leaveDays > 3 && (
                    <p className="text-xs text-blue-700 mt-1">
                      Medical certificate will be required for leave longer than 3 days
                    </p>
                  )}
                </div>
              )}

              {/* Balance Check Result */}
              {balanceCheck && (
                <Alert variant={balanceCheck.available ? "default" : "destructive"}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {balanceCheck.message}
                    {balanceCheck.remainingDays !== undefined && (
                      <span className="block mt-1 text-sm">
                        Remaining annual leave: {balanceCheck.remainingDays} days
                      </span>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Medical Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Medical Information</h3>
              
              <FormField
                control={form.control}
                name="symptoms"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Symptoms/Condition *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Please describe your symptoms or medical condition..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="requiresHospitalization"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Requires Hospitalization</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          Check if you need to be hospitalized
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isContagious"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Contagious Condition</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          Check if your condition is contagious
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="returnToWorkDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expected Return to Work Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Emergency Contact */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Emergency Contact</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="emergencyContact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Emergency Contact Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Full name of emergency contact" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="emergencyPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Emergency Contact Phone *</FormLabel>
                      <FormControl>
                        <Input placeholder="+62 xxx xxxx xxxx" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Additional Notes */}
            <FormField
              control={form.control}
              name="additionalNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Any additional information that might be relevant..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Important Notice */}
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> For sick leave longer than 3 days, a medical certificate 
                from a licensed medical practitioner is required. You will be prompted to upload 
                the certificate after submitting this form.
              </AlertDescription>
            </Alert>

            {/* Submit Buttons */}
            <div className="flex space-x-2">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button 
                type="submit" 
                disabled={isSubmitting || (balanceCheck && !balanceCheck.available)}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Sick Leave Request'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
