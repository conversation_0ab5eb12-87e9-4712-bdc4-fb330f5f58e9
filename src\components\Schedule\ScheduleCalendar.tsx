
import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChevronLeft, ChevronRight, Clock, Filter, Users, Calendar as CalendarIcon } from 'lucide-react';
import { Schedule, ScheduleFilters } from '@/types/schedule';
import { ScheduleDetailModal } from './ScheduleDetailModal';
import { useEmployees } from '@/hooks/useEmployees';

interface ScheduleCalendarProps {
  schedules: Schedule[];
}

export function ScheduleCalendar({ schedules }: ScheduleCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [viewType, setViewType] = useState<'month' | 'week'>('month');
  const [filters, setFilters] = useState<ScheduleFilters>({});
  const { employees } = useEmployees();

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  // Filter schedules based on current filters
  const filteredSchedules = useMemo(() => {
    return schedules.filter(schedule => {
      if (filters.employeeId && schedule.employeeId !== filters.employeeId) return false;
      if (filters.shiftType && schedule.shiftType !== filters.shiftType) return false;
      if (filters.status && schedule.status !== filters.status) return false;
      if (filters.department && schedule.employee?.department !== filters.department) return false;
      return true;
    });
  }, [schedules, filters]);

  const getSchedulesForDate = (date: Date | null) => {
    if (!date) return [];
    const dateString = date.toISOString().split('T')[0];
    return filteredSchedules.filter(schedule => schedule.shiftDate === dateString);
  };

  // Get unique departments from employees
  const departments = useMemo(() => {
    return Array.from(new Set(employees.map(emp => emp.department)));
  }, [employees]);

  const handleDateClick = (date: Date | null) => {
    if (date) {
      setSelectedDate(date);
      setIsDetailModalOpen(true);
    }
  };

  // Get week days for week view
  const getWeekDays = (date: Date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day;
    startOfWeek.setDate(diff);

    const weekDays = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      weekDays.push(day);
    }
    return weekDays;
  };

  const weekDays = getWeekDays(currentDate);

  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1));
  };

  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

  const days = getDaysInMonth(currentDate);

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-500';
      case 'afternoon': return 'bg-blue-500';
      case 'night': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <CalendarIcon className="h-5 w-5" />
                <span>Kalender Jadwal</span>
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <h3 className="text-lg font-semibold min-w-[150px] text-center">
                  {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                </h3>
                <Button variant="outline" size="sm" onClick={goToNextMonth}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* View Type Tabs */}
            <Tabs value={viewType} onValueChange={(value) => setViewType(value as 'month' | 'week')}>
              <TabsList>
                <TabsTrigger value="month">Month View</TabsTrigger>
                <TabsTrigger value="week">Week View</TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Filters */}
            <div className="flex flex-wrap gap-2 items-center">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select value={filters.department || ''} onValueChange={(value) => setFilters({...filters, department: value || undefined})}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Departments</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>
                      {dept.charAt(0).toUpperCase() + dept.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.employeeId || ''} onValueChange={(value) => setFilters({...filters, employeeId: value || undefined})}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Employee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Employees</SelectItem>
                  {employees.map((emp) => (
                    <SelectItem key={emp.id} value={emp.id}>
                      {emp.firstName} {emp.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.shiftType || ''} onValueChange={(value) => setFilters({...filters, shiftType: value as any || undefined})}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Shift" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Shifts</SelectItem>
                  <SelectItem value="morning">Morning</SelectItem>
                  <SelectItem value="afternoon">Afternoon</SelectItem>
                  <SelectItem value="night">Night</SelectItem>
                  <SelectItem value="rotating">Rotating</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filters.status || ''} onValueChange={(value) => setFilters({...filters, status: value as any || undefined})}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Status</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="no_show">No Show</SelectItem>
                </SelectContent>
              </Select>

              {Object.keys(filters).some(key => filters[key as keyof ScheduleFilters]) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({})}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={viewType} className="space-y-4">
            <TabsContent value="month">
              <div className="grid grid-cols-7 gap-1 mb-4">
                {dayNames.map((day) => (
                  <div key={day} className="p-2 text-center font-medium text-gray-600 text-sm">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 gap-1">
                {days.map((day, index) => {
                  const daySchedules = getSchedulesForDate(day);
                  const isToday = day && day.toDateString() === new Date().toDateString();

                  return (
                    <div
                      key={index}
                      className={`min-h-[100px] p-2 border rounded cursor-pointer transition-colors ${
                        day ? 'bg-white hover:bg-gray-50' : 'bg-gray-50'
                      } ${isToday ? 'ring-2 ring-blue-500' : ''}`}
                      onClick={() => handleDateClick(day)}
                    >
                      {day && (
                        <>
                          <div className={`text-sm font-medium mb-2 ${
                            isToday ? 'text-blue-600' : 'text-gray-900'
                          }`}>
                            {day.getDate()}
                          </div>
                          <div className="space-y-1">
                            {daySchedules.slice(0, 3).map((schedule) => (
                              <div
                                key={schedule.id}
                                className={`text-xs p-1 rounded text-white truncate ${getShiftTypeColor(schedule.shiftType)}`}
                                title={`${schedule.employee?.firstName} ${schedule.employee?.lastName} - ${schedule.startTime}-${schedule.endTime}`}
                              >
                                {schedule.employee?.firstName?.charAt(0)}{schedule.employee?.lastName?.charAt(0)} {schedule.startTime}
                              </div>
                            ))}
                            {daySchedules.length > 3 && (
                              <div className="text-xs text-gray-500 text-center">
                                +{daySchedules.length - 3} more
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="week">
              <div className="space-y-4">
                <div className="grid grid-cols-8 gap-2">
                  <div className="p-2 text-center font-medium text-gray-600 text-sm">Time</div>
                  {weekDays.map((day) => (
                    <div key={day.toISOString()} className="p-2 text-center">
                      <div className="font-medium text-sm">{dayNames[day.getDay()]}</div>
                      <div className={`text-lg ${day.toDateString() === new Date().toDateString() ? 'text-blue-600 font-bold' : ''}`}>
                        {day.getDate()}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Time slots for week view */}
                {Array.from({ length: 24 }, (_, hour) => (
                  <div key={hour} className="grid grid-cols-8 gap-2 border-t">
                    <div className="p-2 text-xs text-gray-500 text-center">
                      {hour.toString().padStart(2, '0')}:00
                    </div>
                    {weekDays.map((day) => {
                      const daySchedules = getSchedulesForDate(day);
                      const hourSchedules = daySchedules.filter(schedule => {
                        const startHour = parseInt(schedule.startTime.split(':')[0]);
                        const endHour = parseInt(schedule.endTime.split(':')[0]);
                        return hour >= startHour && hour < endHour;
                      });

                      return (
                        <div
                          key={`${day.toISOString()}-${hour}`}
                          className="min-h-[40px] p-1 border-l cursor-pointer hover:bg-gray-50"
                          onClick={() => handleDateClick(day)}
                        >
                          {hourSchedules.map((schedule) => (
                            <div
                              key={schedule.id}
                              className={`text-xs p-1 rounded text-white mb-1 ${getShiftTypeColor(schedule.shiftType)}`}
                              title={`${schedule.employee?.firstName} ${schedule.employee?.lastName} - ${schedule.startTime}-${schedule.endTime}`}
                            >
                              {schedule.employee?.firstName?.charAt(0)}{schedule.employee?.lastName?.charAt(0)}
                            </div>
                          ))}
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Statistics and Legend */}
          <div className="mt-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {filteredSchedules.length}
                  </div>
                  <div className="text-sm text-gray-600">Total Schedules</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {filteredSchedules.filter(s => s.status === 'completed').length}
                  </div>
                  <div className="text-sm text-gray-600">Completed</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {filteredSchedules.filter(s => s.status === 'scheduled').length}
                  </div>
                  <div className="text-sm text-gray-600">Scheduled</div>
                </div>
              </Card>
              <Card className="p-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {new Set(filteredSchedules.map(s => s.employeeId)).size}
                  </div>
                  <div className="text-sm text-gray-600">Employees</div>
                </div>
              </Card>
            </div>

            <div className="flex flex-wrap items-center gap-4 text-sm">
              <span className="font-medium">Legend:</span>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span>Morning Shift</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span>Afternoon Shift</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                <span>Night Shift</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                <span>Rotating Shift</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <ScheduleDetailModal
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        selectedDate={selectedDate}
        schedules={schedules}
      />
    </>
  );
}
