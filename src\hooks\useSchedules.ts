
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Schedule, LeaveRequest } from '@/types/schedule';
import { useToast } from '@/hooks/use-toast';
import { useNotifications } from '@/hooks/useNotifications';
import { emailNotificationService } from '@/services/emailNotifications';

export function useSchedules() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { sendScheduleChangeNotification } = useNotifications();

  const { data: schedules = [], isLoading: isLoadingSchedules } = useQuery({
    queryKey: ['schedules'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('schedules')
        .select(`
          *,
          employees!inner(
            first_name,
            last_name,
            department,
            position
          )
        `)
        .order('shift_date', { ascending: true });

      if (error) throw error;
      
      return data.map((schedule: any): Schedule => ({
        id: schedule.id,
        employeeId: schedule.employee_id,
        shiftDate: schedule.shift_date,
        shiftType: schedule.shift_type,
        startTime: schedule.start_time,
        endTime: schedule.end_time,
        status: schedule.status,
        notes: schedule.notes,
        createdBy: schedule.created_by,
        createdAt: schedule.created_at,
        updatedAt: schedule.updated_at,
        employee: {
          firstName: schedule.employees.first_name,
          lastName: schedule.employees.last_name,
          department: schedule.employees.department,
          position: schedule.employees.position,
        },
      }));
    },
  });

  const { data: leaveRequests = [], isLoading: isLoadingLeaveRequests } = useQuery({
    queryKey: ['leave-requests'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('leave_requests')
        .select(`
          *,
          employees!inner(
            first_name,
            last_name,
            department,
            position
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data.map((request: any): LeaveRequest => ({
        id: request.id,
        employeeId: request.employee_id,
        leaveType: request.leave_type,
        startDate: request.start_date,
        endDate: request.end_date,
        reason: request.reason,
        status: request.status,
        approvedBy: request.approved_by,
        createdAt: request.created_at,
        updatedAt: request.updated_at,
        employee: {
          firstName: request.employees.first_name,
          lastName: request.employees.last_name,
          department: request.employees.department,
          position: request.employees.position,
        },
      }));
    },
  });

  const createScheduleMutation = useMutation({
    mutationFn: async (scheduleData: Omit<Schedule, 'id'>) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('schedules')
        .insert({
          employee_id: scheduleData.employeeId,
          shift_date: scheduleData.shiftDate,
          shift_type: scheduleData.shiftType,
          start_time: scheduleData.startTime,
          end_time: scheduleData.endTime,
          status: scheduleData.status,
          notes: scheduleData.notes,
          created_by: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil ditambahkan",
      });

      // Send notification to the assigned employee
      try {
        await sendScheduleChangeNotification(
          [variables.employeeId],
          variables.shiftDate,
          'created',
          `New ${variables.shiftType} shift assigned from ${variables.startTime} to ${variables.endTime}`
        );

        // Get employee email for email notification
        const { data: employee } = await supabase
          .from('employees')
          .select('email, first_name, last_name, department')
          .eq('id', variables.employeeId)
          .single();

        if (employee) {
          await emailNotificationService.sendScheduleChangeEmail(
            [employee.email],
            `${employee.first_name} ${employee.last_name}`,
            variables.shiftDate,
            'created',
            {
              shiftType: variables.shiftType,
              startTime: variables.startTime,
              endTime: variables.endTime,
              department: employee.department,
            }
          );
        }
      } catch (error) {
        console.error('Error sending notifications:', error);
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateScheduleMutation = useMutation({
    mutationFn: async ({ id, ...scheduleData }: Schedule) => {
      const { data, error } = await supabase
        .from('schedules')
        .update({
          employee_id: scheduleData.employeeId,
          shift_date: scheduleData.shiftDate,
          shift_type: scheduleData.shiftType,
          start_time: scheduleData.startTime,
          end_time: scheduleData.endTime,
          status: scheduleData.status,
          notes: scheduleData.notes,
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: async (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil diperbarui",
      });

      // Send notification to the assigned employee
      try {
        await sendScheduleChangeNotification(
          [variables.employeeId],
          variables.shiftDate,
          'updated',
          `Schedule updated: ${variables.shiftType} shift from ${variables.startTime} to ${variables.endTime}`
        );

        // Get employee email for email notification
        const { data: employee } = await supabase
          .from('employees')
          .select('email, first_name, last_name, department')
          .eq('id', variables.employeeId)
          .single();

        if (employee) {
          await emailNotificationService.sendScheduleChangeEmail(
            [employee.email],
            `${employee.first_name} ${employee.last_name}`,
            variables.shiftDate,
            'updated',
            {
              shiftType: variables.shiftType,
              startTime: variables.startTime,
              endTime: variables.endTime,
              department: employee.department,
            }
          );
        }
      } catch (error) {
        console.error('Error sending notifications:', error);
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteScheduleMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('schedules')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const createLeaveRequestMutation = useMutation({
    mutationFn: async (leaveData: Omit<LeaveRequest, 'id'>) => {
      const { data, error } = await supabase
        .from('leave_requests')
        .insert({
          employee_id: leaveData.employeeId,
          leave_type: leaveData.leaveType,
          start_date: leaveData.startDate,
          end_date: leaveData.endDate,
          reason: leaveData.reason,
          status: leaveData.status || 'pending',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil diajukan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateLeaveRequestMutation = useMutation({
    mutationFn: async ({ id, ...leaveData }: LeaveRequest) => {
      const { data: { user } } = await supabase.auth.getUser();
      const updateData: any = {
        employee_id: leaveData.employeeId,
        leave_type: leaveData.leaveType,
        start_date: leaveData.startDate,
        end_date: leaveData.endDate,
        reason: leaveData.reason,
        status: leaveData.status,
      };

      if (leaveData.status === 'approved' || leaveData.status === 'rejected') {
        updateData.approved_by = user?.id;
      }

      const { data, error } = await supabase
        .from('leave_requests')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Status cuti berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    schedules,
    leaveRequests,
    isLoadingSchedules,
    isLoadingLeaveRequests,
    createSchedule: createScheduleMutation.mutate,
    updateSchedule: updateScheduleMutation.mutate,
    deleteSchedule: deleteScheduleMutation.mutate,
    createLeaveRequest: createLeaveRequestMutation.mutate,
    updateLeaveRequest: updateLeaveRequestMutation.mutate,
    isCreatingSchedule: createScheduleMutation.isPending,
    isUpdatingSchedule: updateScheduleMutation.isPending,
    isDeletingSchedule: deleteScheduleMutation.isPending,
    isCreatingLeaveRequest: createLeaveRequestMutation.isPending,
    isUpdatingLeaveRequest: updateLeaveRequestMutation.isPending,
  };
}
