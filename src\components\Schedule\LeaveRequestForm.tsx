
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useSchedules } from '@/hooks/useSchedules';
import { useEmployees } from '@/hooks/useEmployees';

const leaveRequestSchema = z.object({
  employeeId: z.string().min(1, '<PERSON><PERSON>h karyawan'),
  leaveType: z.string().min(1, '<PERSON>lih jenis cuti'),
  startDate: z.string().min(1, 'Tanggal mulai wajib diisi'),
  endDate: z.string().min(1, 'Tanggal selesai wajib diisi'),
  reason: z.string().optional(),
});

type LeaveRequestFormData = z.infer<typeof leaveRequestSchema>;

interface LeaveRequestFormProps {
  onSuccess?: () => void;
}

export function LeaveRequestForm({ onSuccess }: LeaveRequestFormProps) {
  const { createLeaveRequest, isCreatingLeaveRequest } = useSchedules();
  const { employees } = useEmployees();
  
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
  } = useForm<LeaveRequestFormData>({
    resolver: zodResolver(leaveRequestSchema),
  });

  const leaveTypes = [
    'Cuti Tahunan',
    'Cuti Sakit',
    'Cuti Melahirkan',
    'Cuti Menikah',
    'Cuti Duka',
    'Cuti Lainnya',
  ];

  const onSubmit = (data: LeaveRequestFormData) => {
    // Ensure all required fields are present by creating a properly typed object
    const leaveRequestData = {
      employeeId: data.employeeId,
      leaveType: data.leaveType,
      startDate: data.startDate,
      endDate: data.endDate,
      reason: data.reason,
      status: 'pending' as const,
    };
    
    createLeaveRequest(leaveRequestData);
    reset();
    onSuccess?.();
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="employeeId">Karyawan *</Label>
        <Select onValueChange={(value) => setValue('employeeId', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih karyawan" />
          </SelectTrigger>
          <SelectContent>
            {employees.map((employee) => (
              <SelectItem key={employee.id} value={employee.id}>
                {employee.firstName} {employee.lastName} - {employee.department}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.employeeId && (
          <p className="text-sm text-red-600">{errors.employeeId.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="leaveType">Jenis Cuti *</Label>
        <Select onValueChange={(value) => setValue('leaveType', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih jenis cuti" />
          </SelectTrigger>
          <SelectContent>
            {leaveTypes.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.leaveType && (
          <p className="text-sm text-red-600">{errors.leaveType.message}</p>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startDate">Tanggal Mulai *</Label>
          <Input
            id="startDate"
            type="date"
            {...register('startDate')}
          />
          {errors.startDate && (
            <p className="text-sm text-red-600">{errors.startDate.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="endDate">Tanggal Selesai *</Label>
          <Input
            id="endDate"
            type="date"
            {...register('endDate')}
          />
          {errors.endDate && (
            <p className="text-sm text-red-600">{errors.endDate.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="reason">Alasan Cuti</Label>
        <Textarea
          id="reason"
          placeholder="Jelaskan alasan pengajuan cuti (opsional)"
          {...register('reason')}
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit" disabled={isCreatingLeaveRequest}>
          {isCreatingLeaveRequest ? 'Mengajukan...' : 'Ajukan Cuti'}
        </Button>
      </div>
    </form>
  );
}
