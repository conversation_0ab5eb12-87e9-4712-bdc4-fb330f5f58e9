// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://cqqsubbersnoopxcksqb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNxcXN1YmJlcnNub29weGNrc3FiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MjMwNDUsImV4cCI6MjA2NjM5OTA0NX0.3EGj665SHGumzcrN2QbBZrYsJP4z5Yp7zU6yYlD8k0U";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);