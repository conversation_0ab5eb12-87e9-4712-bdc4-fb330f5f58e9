import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  Calendar,
  FileText,
  AlertTriangle,
  Loader2,
  MessageSquare
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { LeaveRequest, LeaveApproval } from '@/types/schedule';
import { useAuth } from '@/hooks/useAuth';
import { useNotifications } from '@/hooks/useNotifications';
import { emailNotificationService } from '@/services/emailNotifications';

const approvalSchema = z.object({
  comments: z.string().optional(),
});

type ApprovalFormData = z.infer<typeof approvalSchema>;

interface LeaveApprovalWorkflowProps {
  leaveRequest: LeaveRequest;
  onApprovalUpdate?: () => void;
}

export function LeaveApprovalWorkflow({ leaveRequest, onApprovalUpdate }: LeaveApprovalWorkflowProps) {
  const [approvals, setApprovals] = useState<LeaveApproval[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentUserLevel, setCurrentUserLevel] = useState<number | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();
  const { sendNotificationToMultiple } = useNotifications();

  const form = useForm<ApprovalFormData>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      comments: '',
    },
  });

  useEffect(() => {
    loadApprovals();
    checkUserApprovalLevel();
  }, [leaveRequest.id, user]);

  const loadApprovals = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('leave_approvals')
        .select(`
          *,
          approver:employees!approver_id (
            first_name,
            last_name,
            position,
            role
          )
        `)
        .eq('leave_request_id', leaveRequest.id)
        .order('approval_level', { ascending: true });

      if (error) throw error;
      setApprovals(data || []);
    } catch (error) {
      console.error('Error loading approvals:', error);
      toast({
        title: "Error",
        description: "Failed to load approval workflow",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkUserApprovalLevel = async () => {
    if (!user?.email) return;

    try {
      const { data: currentEmployee, error } = await supabase
        .from('employees')
        .select('id, role, department')
        .eq('email', user.email)
        .single();

      if (error || !currentEmployee) return;

      // Determine approval level based on role and department
      let level = null;
      if (currentEmployee.role === 'manager' && currentEmployee.department === leaveRequest.employee?.department) {
        level = 1; // Direct supervisor
      } else if (currentEmployee.role === 'admin' || currentEmployee.role === 'manager') {
        level = 2; // Department head/HR
      }

      setCurrentUserLevel(level);
    } catch (error) {
      console.error('Error checking user approval level:', error);
    }
  };

  const handleApproval = async (action: 'approve' | 'reject', data: ApprovalFormData) => {
    if (!user?.email || currentUserLevel === null) return;

    setIsProcessing(true);
    try {
      // Get current user's employee record
      const { data: currentEmployee, error: empError } = await supabase
        .from('employees')
        .select('id, first_name, last_name')
        .eq('email', user.email)
        .single();

      if (empError || !currentEmployee) throw new Error('Employee not found');

      // Create or update approval record
      const approvalData = {
        leave_request_id: leaveRequest.id,
        approver_id: currentEmployee.id,
        approval_level: currentUserLevel,
        status: action === 'approve' ? 'approved' : 'rejected',
        comments: data.comments || null,
        approved_at: new Date().toISOString(),
      };

      const { error: approvalError } = await supabase
        .from('leave_approvals')
        .upsert(approvalData, {
          onConflict: 'leave_request_id,approver_id,approval_level'
        });

      if (approvalError) throw approvalError;

      // Check if this is the final approval level
      const isLastLevel = currentUserLevel === 2 || action === 'reject';
      
      if (isLastLevel) {
        // Update leave request status
        const { error: updateError } = await supabase
          .from('leave_requests')
          .update({
            status: action === 'approve' ? 'approved' : 'rejected',
            approved_by: currentEmployee.id,
          })
          .eq('id', leaveRequest.id);

        if (updateError) throw updateError;
      }

      // Send notifications
      await sendNotifications(action, data.comments, isLastLevel);

      toast({
        title: "Success",
        description: `Leave request ${action}d successfully`,
      });

      if (onApprovalUpdate) {
        onApprovalUpdate();
      }

      loadApprovals();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${action} leave request`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const sendNotifications = async (action: 'approve' | 'reject', comments?: string, isFinal?: boolean) => {
    try {
      const notifications = [];
      
      // Notify the employee
      notifications.push({
        recipientId: leaveRequest.employeeId,
        title: `Leave Request ${action === 'approve' ? 'Approved' : 'Rejected'}${isFinal ? '' : ' (Pending Further Approval)'}`,
        message: `Your ${leaveRequest.leaveType} leave request from ${leaveRequest.startDate} to ${leaveRequest.endDate} has been ${action}d${comments ? `. Comments: ${comments}` : ''}`,
        type: `leave_${action}d`,
        relatedId: leaveRequest.id,
      });

      // Send in-app notifications
      await sendNotificationToMultiple(
        notifications.map(n => n.recipientId),
        notifications[0].title,
        notifications[0].message,
        notifications[0].type,
        leaveRequest.id
      );

      // Send email notifications
      if (leaveRequest.employee?.email) {
        await emailNotificationService.sendLeaveResponseEmail(
          leaveRequest.employee.email,
          `${leaveRequest.employee.firstName} ${leaveRequest.employee.lastName}`,
          leaveRequest.leaveType,
          leaveRequest.startDate,
          leaveRequest.endDate,
          action === 'approve' ? 'approved' : 'rejected',
          comments
        );
      }
    } catch (error) {
      console.error('Error sending notifications:', error);
    }
  };

  const getApprovalLevelName = (level: number) => {
    switch (level) {
      case 1: return 'Direct Supervisor';
      case 2: return 'Department Head/HR';
      case 3: return 'Senior Management';
      default: return `Level ${level}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-4 w-4" />;
      case 'rejected': return <XCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const canApprove = currentUserLevel !== null && 
    !approvals.some(a => a.approverId === user?.email && a.status !== 'pending') &&
    leaveRequest.status === 'pending';

  const totalDays = Math.ceil(
    (new Date(leaveRequest.endDate).getTime() - new Date(leaveRequest.startDate).getTime()) / (1000 * 60 * 60 * 24)
  ) + 1;

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Leave Request Approval Workflow</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Leave Request Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="font-medium text-sm text-gray-600">Employee</h4>
            <p className="font-semibold">{leaveRequest.employee?.firstName} {leaveRequest.employee?.lastName}</p>
            <p className="text-sm text-gray-600">{leaveRequest.employee?.department}</p>
          </div>
          <div>
            <h4 className="font-medium text-sm text-gray-600">Leave Details</h4>
            <p className="font-semibold">{leaveRequest.leaveType}</p>
            <p className="text-sm text-gray-600">
              {new Date(leaveRequest.startDate).toLocaleDateString('id-ID')} - {new Date(leaveRequest.endDate).toLocaleDateString('id-ID')} ({totalDays} days)
            </p>
          </div>
          {leaveRequest.reason && (
            <div className="md:col-span-2">
              <h4 className="font-medium text-sm text-gray-600">Reason</h4>
              <p className="text-sm">{leaveRequest.reason}</p>
            </div>
          )}
        </div>

        {/* Approval Workflow */}
        <div className="space-y-4">
          <h4 className="font-medium">Approval Workflow</h4>
          
          {/* Generate approval levels if none exist */}
          {approvals.length === 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No approval workflow has been initiated for this request.
              </AlertDescription>
            </Alert>
          )}

          {/* Show approval steps */}
          <div className="space-y-3">
            {[1, 2].map((level) => {
              const approval = approvals.find(a => a.approvalLevel === level);
              const isCurrentLevel = level === currentUserLevel;
              
              return (
                <div key={level} className={`flex items-center space-x-4 p-3 border rounded-lg ${
                  isCurrentLevel && canApprove ? 'border-blue-500 bg-blue-50' : ''
                }`}>
                  <div className="flex-shrink-0">
                    {approval ? (
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(approval.status)}
                        <Badge className={getStatusColor(approval.status)}>
                          {approval.status}
                        </Badge>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <Badge variant="outline">Pending</Badge>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <p className="font-medium">{getApprovalLevelName(level)}</p>
                    {approval?.approver && (
                      <p className="text-sm text-gray-600">
                        {approval.approver.firstName} {approval.approver.lastName} - {approval.approver.position}
                      </p>
                    )}
                    {approval?.comments && (
                      <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                        <MessageSquare className="h-3 w-3 inline mr-1" />
                        {approval.comments}
                      </div>
                    )}
                    {approval?.approvedAt && (
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(approval.approvedAt).toLocaleString('id-ID')}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Approval Actions */}
        {canApprove && (
          <div className="border-t pt-4">
            <h4 className="font-medium mb-4">Take Action</h4>
            <Form {...form}>
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="comments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comments (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Add any comments about your decision..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex space-x-2">
                  <Button
                    onClick={form.handleSubmit((data) => handleApproval('approve', data))}
                    disabled={isProcessing}
                    className="flex-1"
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Approve
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={form.handleSubmit((data) => handleApproval('reject', data))}
                    disabled={isProcessing}
                    className="flex-1"
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <XCircle className="h-4 w-4 mr-2" />
                    )}
                    Reject
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
