# Comprehensive Dummy Data Setup
## Employee Management System (Manajemen Karyawan RS)

This repository contains comprehensive dummy data for testing the Employee Management System, specifically designed for the **Schedule Management** and **Leave and Permit Management** modules.

## 🎯 Overview

The dummy data includes:
- **16 employees** across 9 hospital departments
- **1,500+ schedule entries** covering 4 months of data
- **150+ leave requests** with various types and statuses
- **Complete approval workflows** with multi-level approvals
- **Medical certificates** for sick leave validation
- **Schedule replacement system** for shift substitutions
- **Comprehensive notification system** for all activities
- **Leave balance tracking** following Indonesian labor standards

## 🚀 Quick Setup

### Option 1: Using Supabase CLI
```bash
# Navigate to project directory
cd manajemen_karyawan_rs

# Run the migration
supabase db reset
supabase migration up
```

### Option 2: Using PostgreSQL directly
```bash
# Connect to your database and run
psql -d your_database -f supabase/migrations/20250701120000_comprehensive_dummy_data.sql
```

### Option 3: Using the verification script
```bash
# Run with verification
psql -d your_database -f scripts/load_dummy_data.sql
```

## 📊 Data Summary

### Employees by Department
- **Emergency**: 2 employees (24/7 coverage)
- **Surgery**: 2 employees (OR specialists)
- **Pediatrics**: 2 employees (child care)
- **Cardiology**: 2 employees (heart specialists)
- **Orthopedics**: 2 employees (bone/joint care)
- **Pharmacy**: 2 employees (medication management)
- **Laboratory**: 1 employee (lab testing)
- **Radiology**: 1 employee (imaging)
- **Administration**: 2 employees (management)

### Schedule Coverage
- **Morning Shifts**: 07:00 - 15:00
- **Afternoon Shifts**: 15:00 - 23:00
- **Night Shifts**: 23:00 - 07:00
- **Rotating Shifts**: Dynamic assignment
- **Weekend Coverage**: Emergency department priority
- **Holiday Scheduling**: Realistic coverage patterns

### Leave Types (Indonesian Standards)
- **Cuti Tahunan**: 12-15 days annual leave
- **Cuti Sakit**: Sick leave with medical certificates
- **Cuti Melahirkan**: 90-day maternity leave
- **Cuti Menikah**: 3-day marriage leave
- **Cuti Duka Cita**: 2-3 day bereavement leave
- **Cuti Haji/Umroh**: Religious pilgrimage leave
- **Cuti Darurat**: Emergency family leave
- **Cuti Pendidikan**: Educational/training leave

## 🔍 Testing Scenarios

### Schedule Management
✅ **Calendar View Testing**
- Multi-employee daily schedules
- Shift type visualization
- Weekend/holiday coverage
- Department-wise filtering

✅ **Employee Schedule View**
- Individual employee schedules
- Shift pattern analysis
- Workload distribution
- Time-off integration

✅ **Monthly Schedule View**
- Department-wide planning
- Resource allocation
- Coverage gap identification
- Shift rotation patterns

✅ **Schedule Replacement**
- Emergency substitutions
- Approval workflow testing
- Notification system
- Conflict resolution

### Leave Management
✅ **Leave Application Process**
- Various leave type submissions
- Date range validation
- Reason documentation
- File attachment simulation

✅ **Approval Workflow**
- Multi-level approval chain
- Comments and feedback
- Status change tracking
- Email notification triggers

✅ **Leave Balance Tracking**
- Annual entitlement calculation
- Real-time usage monitoring
- Remaining balance display
- Year-over-year tracking

✅ **Medical Certificate Management**
- Document upload simulation
- Verification process
- Compliance checking
- Audit trail maintenance

✅ **Reporting and Analytics**
- Leave usage patterns
- Department-wise analysis
- Trend identification
- Compliance reporting

## 🏥 Indonesian Healthcare Compliance

The dummy data follows Indonesian labor regulations:

### Leave Entitlements
- **Minimum Annual Leave**: 12 days per year
- **Senior Employee Bonus**: 15 days (5+ years service)
- **Maternity Leave**: 90 days (3 months)
- **Marriage Leave**: 3 consecutive days
- **Bereavement Leave**: 2-3 days based on relationship
- **Sick Leave**: Medical certificate required >2 days
- **Religious Leave**: Haji/Umroh provisions

### Working Hours
- **Standard Shift**: 8 hours per day
- **Maximum Weekly**: 40 hours
- **Overtime Regulations**: Tracked and compensated
- **Rest Periods**: Mandatory between shifts
- **Weekend Work**: Emergency services only

## 📁 File Structure

```
├── supabase/migrations/
│   └── 20250701120000_comprehensive_dummy_data.sql  # Main migration file
├── scripts/
│   └── load_dummy_data.sql                          # Verification script
├── docs/
│   └── DUMMY_DATA_DOCUMENTATION.md                  # Detailed documentation
└── README_DUMMY_DATA.md                             # This file
```

## 🔧 Database Schema Extensions

The dummy data adds several new tables:

### Core Tables Enhanced
- `employees` - Extended with realistic Indonesian data
- `schedules` - Comprehensive shift patterns
- `leave_requests` - Various leave types and statuses

### New Tables Added
- `leave_balances` - Annual leave tracking
- `leave_approvals` - Multi-level approval workflow
- `medical_certificates` - Sick leave documentation
- `schedule_replacements` - Shift substitution system
- `notifications` - System-wide notifications

## 🚨 Important Notes

### Development Use Only
- This data is for **testing and development** purposes only
- Contains **fictional employee information**
- Should **not be used in production** environments

### Data Reset
```sql
-- To clear all dummy data
TRUNCATE TABLE notifications CASCADE;
TRUNCATE TABLE schedule_replacements CASCADE;
TRUNCATE TABLE medical_certificates CASCADE;
TRUNCATE TABLE leave_approvals CASCADE;
TRUNCATE TABLE leave_balances CASCADE;
TRUNCATE TABLE leave_requests CASCADE;
TRUNCATE TABLE schedules CASCADE;
-- Keep base employees or truncate if needed
```

### Performance Considerations
- **Indexes created** for optimal query performance
- **Foreign key relationships** maintained
- **Data volume suitable** for load testing
- **Realistic distribution** for accurate testing

## 📞 Support

For questions about the dummy data:
1. Check the detailed documentation: `docs/DUMMY_DATA_DOCUMENTATION.md`
2. Review the migration file: `supabase/migrations/20250701120000_comprehensive_dummy_data.sql`
3. Run the verification script: `scripts/load_dummy_data.sql`

## 🎉 Ready to Test!

After loading the dummy data, you can:
- Test all schedule management features
- Validate leave request workflows
- Check notification systems
- Verify reporting functionality
- Perform load testing
- Validate Indonesian compliance features

The system is now ready for comprehensive testing with realistic hospital data! 🏥
