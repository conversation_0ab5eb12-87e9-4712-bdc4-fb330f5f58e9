import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  TrendingUp, 
  Users, 
  BarChart3, 
  Settings,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { LeaveBalanceCard } from '@/components/Schedule/LeaveBalanceCard';
import { leaveBalanceService } from '@/services/leaveBalanceService';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

export default function LeaveBalance() {
  const [isProcessingCarryOver, setIsProcessingCarryOver] = useState(false);
  const [isInitializingBalances, setIsInitializingBalances] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('');
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    checkUserRole();
  }, [user]);

  const checkUserRole = async () => {
    if (!user?.email) return;

    try {
      const { data: employee, error } = await supabase
        .from('employees')
        .select('role')
        .eq('email', user.email)
        .single();

      if (!error && employee) {
        setCurrentUserRole(employee.role);
      }
    } catch (error) {
      console.error('Error checking user role:', error);
    }
  };

  const handleProcessYearEndCarryOver = async () => {
    setIsProcessingCarryOver(true);
    try {
      const currentYear = new Date().getFullYear();
      const success = await leaveBalanceService.processYearEndCarryOver(currentYear - 1);
      
      if (success) {
        toast({
          title: "Success",
          description: "Year-end carry over processed successfully",
        });
      } else {
        throw new Error('Failed to process carry over');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process year-end carry over",
        variant: "destructive",
      });
    } finally {
      setIsProcessingCarryOver(false);
    }
  };

  const handleInitializeAllBalances = async () => {
    setIsInitializingBalances(true);
    try {
      const currentYear = new Date().getFullYear();
      
      // Get all active employees
      const { data: employees, error } = await supabase
        .from('employees')
        .select('id, join_date')
        .eq('status', 'active');

      if (error) throw error;

      // Initialize balances for all employees
      const initPromises = employees?.map(emp => 
        leaveBalanceService.initializeEmployeeBalance(emp.id, emp.join_date, currentYear)
      ) || [];

      const results = await Promise.all(initPromises);
      const successCount = results.filter(Boolean).length;

      toast({
        title: "Success",
        description: `Initialized leave balances for ${successCount} employees`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to initialize leave balances",
        variant: "destructive",
      });
    } finally {
      setIsInitializingBalances(false);
    }
  };

  const generateDepartmentReport = async () => {
    try {
      const currentYear = new Date().getFullYear();
      const report = await leaveBalanceService.generateUsageReport(currentYear);
      
      // Create CSV content
      const headers = [
        'Employee Name',
        'Department',
        'Position',
        'Join Date',
        'Annual Leave Total',
        'Annual Leave Used',
        'Annual Leave Remaining',
        'Sick Leave Used',
        'Emergency Leave Used',
        'Maternity Leave Used',
        'Usage Percentage',
        'Status'
      ];

      const csvContent = [
        headers.join(','),
        ...report.map(row => [
          `"${row.employeeName}"`,
          row.department,
          row.position,
          row.joinDate,
          row.annualLeaveTotal,
          row.annualLeaveUsed,
          row.annualLeaveRemaining,
          row.sickLeaveUsed,
          row.emergencyLeaveUsed,
          row.maternityLeaveUsed,
          `${row.usagePercentage}%`,
          row.status
        ].join(','))
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leave_usage_report_${currentYear}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Department report exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate department report",
        variant: "destructive",
      });
    }
  };

  const isManager = currentUserRole === 'manager' || currentUserRole === 'admin';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Leave Balance Management</h1>
          <p className="text-gray-600">Track and manage employee leave balances</p>
        </div>
        
        {isManager && (
          <div className="flex space-x-2">
            <Button
              onClick={generateDepartmentReport}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export Report</span>
            </Button>
            <Button
              onClick={handleInitializeAllBalances}
              disabled={isInitializingBalances}
              variant="outline"
              className="flex items-center space-x-2"
            >
              {isInitializingBalances ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Settings className="h-4 w-4" />
              )}
              <span>Initialize Balances</span>
            </Button>
            <Button
              onClick={handleProcessYearEndCarryOver}
              disabled={isProcessingCarryOver}
              className="flex items-center space-x-2"
            >
              {isProcessingCarryOver ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <TrendingUp className="h-4 w-4" />
              )}
              <span>Process Carry Over</span>
            </Button>
          </div>
        )}
      </div>

      {/* Important Notices */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Leave Policy:</strong> Annual leave entitlement is 12 days per year. 
            Up to 6 days can be carried over to the next year.
          </AlertDescription>
        </Alert>
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Reminder:</strong> Carried over leave days expire after 3 months 
            if not used. Plan your leave accordingly.
          </AlertDescription>
        </Alert>
      </div>

      {/* Main Content */}
      <Tabs defaultValue={isManager ? "overview" : "personal"} className="space-y-4">
        <TabsList>
          <TabsTrigger value="personal" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>My Balance</span>
          </TabsTrigger>
          {isManager && (
            <>
              <TabsTrigger value="overview" className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>All Employees</span>
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span>Analytics</span>
              </TabsTrigger>
            </>
          )}
        </TabsList>

        <TabsContent value="personal">
          <LeaveBalanceCard showAllEmployees={false} />
        </TabsContent>

        {isManager && (
          <>
            <TabsContent value="overview">
              <LeaveBalanceCard showAllEmployees={true} />
            </TabsContent>

            <TabsContent value="analytics">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="h-5 w-5" />
                      <span>Leave Usage Analytics</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Advanced analytics dashboard coming soon</p>
                      <p className="text-sm">This will include usage trends, department comparisons, and predictive analytics</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">85%</div>
                      <div className="text-sm text-gray-600">Average Usage Rate</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">15</div>
                      <div className="text-sm text-gray-600">Employees with High Balance</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-orange-600">8</div>
                      <div className="text-sm text-gray-600">Low Balance Alerts</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">45</div>
                      <div className="text-sm text-gray-600">Days to Carry Over</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Department Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle>Department Usage Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { dept: 'Emergency', usage: 78, total: 120, color: 'bg-red-500' },
                        { dept: 'Surgery', usage: 65, total: 96, color: 'bg-blue-500' },
                        { dept: 'Pediatrics', usage: 45, total: 72, color: 'bg-green-500' },
                        { dept: 'Cardiology', usage: 38, total: 60, color: 'bg-purple-500' },
                        { dept: 'Pharmacy', usage: 28, total: 48, color: 'bg-yellow-500' },
                      ].map((dept) => (
                        <div key={dept.dept} className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="font-medium">{dept.dept}</span>
                            <span>{dept.usage}/{dept.total} days used</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${dept.color}`}
                              style={{ width: `${(dept.usage / dept.total) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  );
}
