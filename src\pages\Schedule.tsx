import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Plus, Filter, Building } from 'lucide-react';
import { useSchedules } from '@/hooks/useSchedules';
import { useEmployees } from '@/hooks/useEmployees';
import { ScheduleCalendar } from '@/components/Schedule/ScheduleCalendar';
import { EmployeeScheduleView } from '@/components/Schedule/EmployeeScheduleView';
import { MonthlyScheduleView } from '@/components/Schedule/MonthlyScheduleView';
import { ScheduleForm } from '@/components/Schedule/ScheduleForm';
import { LeaveRequestForm } from '@/components/Schedule/LeaveRequestForm';
import { LeaveRequestsList } from '@/components/Schedule/LeaveRequestsList';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

export default function Schedule() {
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [isLeaveDialogOpen, setIsLeaveDialogOpen] = useState(false);
  const { schedules, leaveRequests, isLoadingSchedules, isLoadingLeaveRequests } = useSchedules();
  const { employees } = useEmployees();

  const todaySchedules = schedules.filter(schedule => 
    schedule.shiftDate === new Date().toISOString().split('T')[0]
  );

  const pendingLeaveRequests = leaveRequests.filter(request => 
    request.status === 'pending'
  );

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'no_show': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoadingSchedules || isLoadingLeaveRequests) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manajemen Jadwal</h1>
          <p className="text-gray-600">Kelola jadwal kerja dan cuti karyawan</p>
        </div>
        <div className="flex space-x-2">
          <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Tambah Jadwal
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Tambah Jadwal Kerja</DialogTitle>
              </DialogHeader>
              <ScheduleForm onSuccess={() => setIsScheduleDialogOpen(false)} />
            </DialogContent>
          </Dialog>
          
          <Dialog open={isLeaveDialogOpen} onOpenChange={setIsLeaveDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Calendar className="w-4 h-4 mr-2" />
                Ajukan Cuti
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Ajukan Permohonan Cuti</DialogTitle>
              </DialogHeader>
              <LeaveRequestForm onSuccess={() => setIsLeaveDialogOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Jadwal Hari Ini</p>
                <p className="text-2xl font-bold">{todaySchedules.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Karyawan</p>
                <p className="text-2xl font-bold">{employees.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Cuti Pending</p>
                <p className="text-2xl font-bold">{pendingLeaveRequests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Jadwal</p>
                <p className="text-2xl font-bold">{schedules.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="calendar" className="space-y-4">
        <TabsList>
          <TabsTrigger value="calendar">Kalender Jadwal</TabsTrigger>
          <TabsTrigger value="employee">Jadwal Karyawan</TabsTrigger>
          <TabsTrigger value="monthly">Jadwal Bulanan</TabsTrigger>
          <TabsTrigger value="schedules">Daftar Jadwal</TabsTrigger>
          <TabsTrigger value="leaves">Permohonan Cuti</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar">
          <ScheduleCalendar schedules={schedules} />
        </TabsContent>

        <TabsContent value="employee">
          <EmployeeScheduleView schedules={schedules} />
        </TabsContent>

        <TabsContent value="monthly">
          <MonthlyScheduleView schedules={schedules} />
        </TabsContent>

        <TabsContent value="schedules">
          <Card>
            <CardHeader>
              <CardTitle>Daftar Jadwal Kerja</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {schedules.map((schedule) => (
                  <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">
                          {schedule.employee?.firstName} {schedule.employee?.lastName}
                        </p>
                        <p className="text-sm text-gray-600">
                          {schedule.employee?.department} - {schedule.employee?.position}
                        </p>
                      </div>
                      <div className="text-sm">
                        <p className="font-medium">{new Date(schedule.shiftDate).toLocaleDateString('id-ID')}</p>
                        <p className="text-gray-600">{schedule.startTime} - {schedule.endTime}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getShiftTypeColor(schedule.shiftType)}>
                        {schedule.shiftType}
                      </Badge>
                      <Badge className={getStatusColor(schedule.status)}>
                        {schedule.status}
                      </Badge>
                    </div>
                  </div>
                ))}
                
                {schedules.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Belum ada jadwal yang dibuat</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leaves">
          <LeaveRequestsList leaveRequests={leaveRequests} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
