-- <PERSON><PERSON><PERSON> to load comprehensive dummy data and verify results
-- Run this script to populate the database with test data

-- Load the comprehensive dummy data
\i supabase/migrations/20250701120000_comprehensive_dummy_data.sql

-- Verify data loading with summary queries
\echo '=== DUMMY DATA LOADING SUMMARY ==='
\echo ''

\echo '1. EMPLOYEE DATA SUMMARY:'
SELECT 
  department,
  role,
  COUNT(*) as employee_count,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count
FROM employees 
GROUP BY department, role 
ORDER BY department, role;

\echo ''
\echo '2. SCHEDULE DATA SUMMARY:'
SELECT 
  shift_type,
  status,
  COUNT(*) as schedule_count,
  MIN(shift_date) as earliest_date,
  MAX(shift_date) as latest_date
FROM schedules 
GROUP BY shift_type, status 
ORDER BY shift_type, status;

\echo ''
\echo '3. LEAVE REQUEST SUMMARY:'
SELECT 
  leave_type,
  status,
  COUNT(*) as request_count,
  AVG(end_date::date - start_date::date + 1) as avg_duration_days
FROM leave_requests 
GROUP BY leave_type, status 
ORDER BY leave_type, status;

\echo ''
\echo '4. LEAVE BALANCE SUMMARY:'
SELECT 
  year,
  COUNT(*) as employee_count,
  AVG(annual_leave_total) as avg_total_leave,
  AVG(annual_leave_used) as avg_used_leave,
  AVG(annual_leave_remaining) as avg_remaining_leave
FROM leave_balances 
GROUP BY year 
ORDER BY year;

\echo ''
\echo '5. MEDICAL CERTIFICATE SUMMARY:'
SELECT 
  verified,
  COUNT(*) as certificate_count
FROM medical_certificates 
GROUP BY verified;

\echo ''
\echo '6. SCHEDULE REPLACEMENT SUMMARY:'
SELECT 
  status,
  COUNT(*) as replacement_count
FROM schedule_replacements 
GROUP BY status 
ORDER BY status;

\echo ''
\echo '7. NOTIFICATION SUMMARY:'
SELECT 
  type,
  read,
  COUNT(*) as notification_count
FROM notifications 
GROUP BY type, read 
ORDER BY type, read;

\echo ''
\echo '8. OVERALL DATA SUMMARY:'
SELECT 
  'Employees' as table_name,
  COUNT(*) as record_count
FROM employees
UNION ALL
SELECT 
  'Schedules' as table_name,
  COUNT(*) as record_count
FROM schedules
UNION ALL
SELECT 
  'Leave Requests' as table_name,
  COUNT(*) as record_count
FROM leave_requests
UNION ALL
SELECT 
  'Leave Balances' as table_name,
  COUNT(*) as record_count
FROM leave_balances
UNION ALL
SELECT 
  'Leave Approvals' as table_name,
  COUNT(*) as record_count
FROM leave_approvals
UNION ALL
SELECT 
  'Medical Certificates' as table_name,
  COUNT(*) as record_count
FROM medical_certificates
UNION ALL
SELECT 
  'Schedule Replacements' as table_name,
  COUNT(*) as record_count
FROM schedule_replacements
UNION ALL
SELECT 
  'Notifications' as table_name,
  COUNT(*) as record_count
FROM notifications;

\echo ''
\echo '=== DUMMY DATA LOADING COMPLETED ==='
\echo 'The database has been populated with comprehensive test data.'
\echo 'Check the documentation at docs/DUMMY_DATA_DOCUMENTATION.md for details.'
