import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, RotateCcw, Calendar, Users, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useEmployees } from '@/hooks/useEmployees';

const rotationSchema = z.object({
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  department: z.string().optional(),
  rotationType: z.enum(['weekly', 'monthly', 'custom']),
  rotationPattern: z.array(z.string()).min(1, 'Select at least one shift type'),
  selectedEmployees: z.array(z.string()).min(2, 'Select at least 2 employees'),
  preserveWeekends: z.boolean().default(true),
});

type RotationFormData = z.infer<typeof rotationSchema>;

interface ShiftRotationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const rotationTypes = [
  { value: 'weekly', label: 'Weekly Rotation' },
  { value: 'monthly', label: 'Monthly Rotation' },
  { value: 'custom', label: 'Custom Pattern' },
];

const shiftTypes = [
  { value: 'morning', label: 'Morning Shift' },
  { value: 'afternoon', label: 'Afternoon Shift' },
  { value: 'night', label: 'Night Shift' },
];

export function ShiftRotationDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: ShiftRotationDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();
  const { employees } = useEmployees();

  const form = useForm<RotationFormData>({
    resolver: zodResolver(rotationSchema),
    defaultValues: {
      startDate: '',
      endDate: '',
      department: '',
      rotationType: 'weekly',
      rotationPattern: ['morning', 'afternoon', 'night'],
      selectedEmployees: [],
      preserveWeekends: true,
    },
  });

  const departments = Array.from(new Set(employees.map(emp => emp.department)));
  const selectedDepartment = form.watch('department');
  const availableEmployees = selectedDepartment 
    ? employees.filter(emp => emp.department === selectedDepartment && emp.status === 'active')
    : employees.filter(emp => emp.status === 'active');

  const generateRotationPreview = async (data: RotationFormData) => {
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    const selectedEmps = employees.filter(emp => data.selectedEmployees.includes(emp.id));
    const pattern = data.rotationPattern;
    
    const preview = [];
    let currentDate = new Date(startDate);
    let employeeIndex = 0;
    let shiftIndex = 0;

    while (currentDate <= endDate) {
      const dayOfWeek = currentDate.getDay();
      
      // Skip weekends if preserveWeekends is true
      if (data.preserveWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
        currentDate.setDate(currentDate.getDate() + 1);
        continue;
      }

      const currentEmployee = selectedEmps[employeeIndex % selectedEmps.length];
      const currentShift = pattern[shiftIndex % pattern.length];

      preview.push({
        date: new Date(currentDate),
        employee: currentEmployee,
        shiftType: currentShift,
        dayOfWeek: currentDate.toLocaleDateString('id-ID', { weekday: 'long' }),
      });

      // Advance to next employee and shift based on rotation type
      if (data.rotationType === 'weekly') {
        if (dayOfWeek === 5) { // Friday - end of work week
          employeeIndex++;
          shiftIndex++;
        }
      } else if (data.rotationType === 'monthly') {
        if (currentDate.getDate() === 1) { // First day of month
          employeeIndex++;
          shiftIndex++;
        }
      } else { // custom - rotate daily
        employeeIndex++;
        if (employeeIndex % selectedEmps.length === 0) {
          shiftIndex++;
        }
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    setPreviewData(preview);
    setShowPreview(true);
  };

  const handleGenerateRotation = async (data: RotationFormData) => {
    setIsGenerating(true);
    try {
      // Generate the rotation schedule
      await generateRotationPreview(data);
      
      // Create schedules in database
      const schedulesToCreate = previewData.map(item => ({
        employee_id: item.employee.id,
        shift_date: item.date.toISOString().split('T')[0],
        shift_type: item.shiftType,
        start_time: getShiftStartTime(item.shiftType),
        end_time: getShiftEndTime(item.shiftType),
        status: 'scheduled',
        notes: `Auto-generated rotation schedule (${data.rotationType})`,
      }));

      // Check for conflicts
      const dateRange = schedulesToCreate.map(s => s.shift_date);
      const minDate = Math.min(...dateRange.map(d => new Date(d).getTime()));
      const maxDate = Math.max(...dateRange.map(d => new Date(d).getTime()));

      const { data: existingSchedules, error: checkError } = await supabase
        .from('schedules')
        .select('employee_id, shift_date')
        .gte('shift_date', new Date(minDate).toISOString().split('T')[0])
        .lte('shift_date', new Date(maxDate).toISOString().split('T')[0])
        .in('employee_id', data.selectedEmployees);

      if (checkError) throw checkError;

      // Filter out conflicting schedules
      const conflicts = existingSchedules || [];
      const nonConflictingSchedules = schedulesToCreate.filter(schedule => 
        !conflicts.some(conflict => 
          conflict.employee_id === schedule.employee_id && 
          conflict.shift_date === schedule.shift_date
        )
      );

      if (nonConflictingSchedules.length === 0) {
        toast({
          title: "No Schedules Created",
          description: "All proposed schedules conflict with existing ones.",
          variant: "destructive",
        });
        return;
      }

      // Insert non-conflicting schedules
      const { error: insertError } = await supabase
        .from('schedules')
        .insert(nonConflictingSchedules);

      if (insertError) throw insertError;

      // Send notifications to affected employees
      const notificationsToSend = data.selectedEmployees.map(employeeId => ({
        recipient_id: employeeId,
        title: 'New Rotation Schedule',
        message: `A new ${data.rotationType} rotation schedule has been created for you from ${data.startDate} to ${data.endDate}.`,
        type: 'schedule_change',
      }));

      await supabase
        .from('notifications')
        .insert(notificationsToSend);

      toast({
        title: "Success",
        description: `Created ${nonConflictingSchedules.length} rotation schedules${conflicts.length > 0 ? ` (${conflicts.length} conflicts skipped)` : ''}`,
      });

      if (onSuccess) {
        onSuccess();
      }
      
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate rotation schedule. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getShiftStartTime = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return '07:00';
      case 'afternoon': return '15:00';
      case 'night': return '23:00';
      default: return '07:00';
    }
  };

  const getShiftEndTime = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return '15:00';
      case 'afternoon': return '23:00';
      case 'night': return '07:00';
      default: return '15:00';
    }
  };

  const handleClose = () => {
    form.reset();
    setPreviewData([]);
    setShowPreview(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <RotateCcw className="h-5 w-5" />
            <span>Shift Rotation Generator</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleGenerateRotation)} className="space-y-4">
              {/* Date Range */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Department and Rotation Type */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department (Optional)</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department or leave empty for all" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">All Departments</SelectItem>
                          {departments.map((dept) => (
                            <SelectItem key={dept} value={dept}>
                              {dept.charAt(0).toUpperCase() + dept.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rotationType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rotation Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {rotationTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Rotation Pattern */}
              <FormField
                control={form.control}
                name="rotationPattern"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shift Pattern</FormLabel>
                    <div className="grid grid-cols-3 gap-2">
                      {shiftTypes.map((shift) => (
                        <div key={shift.value} className="flex items-center space-x-2">
                          <Checkbox
                            checked={field.value?.includes(shift.value)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, shift.value]);
                              } else {
                                field.onChange(field.value?.filter(s => s !== shift.value));
                              }
                            }}
                          />
                          <label className="text-sm">{shift.label}</label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Employee Selection */}
              <FormField
                control={form.control}
                name="selectedEmployees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Employees</FormLabel>
                    <div className="max-h-40 overflow-y-auto border rounded-md p-2 space-y-2">
                      {availableEmployees.map((employee) => (
                        <div key={employee.id} className="flex items-center space-x-2">
                          <Checkbox
                            checked={field.value?.includes(employee.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, employee.id]);
                              } else {
                                field.onChange(field.value?.filter(id => id !== employee.id));
                              }
                            }}
                          />
                          <div className="flex-1">
                            <span className="text-sm font-medium">
                              {employee.firstName} {employee.lastName}
                            </span>
                            <div className="flex space-x-1">
                              <Badge variant="outline" className="text-xs">
                                {employee.department}
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                {employee.position}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Options */}
              <FormField
                control={form.control}
                name="preserveWeekends"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Preserve Weekends</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Skip weekends in rotation schedule
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <div className="flex justify-between">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => generateRotationPreview(form.getValues())}
                  disabled={!form.formState.isValid}
                >
                  Preview Rotation
                </Button>
                
                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isGenerating || !showPreview}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <RotateCcw className="w-4 h-4 mr-2" />
                        Generate Rotation
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>

          {/* Preview Section */}
          {showPreview && previewData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Rotation Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-60 overflow-y-auto">
                  <div className="space-y-2">
                    {previewData.slice(0, 14).map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded text-sm">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span>{item.date.toLocaleDateString('id-ID')} ({item.dayOfWeek})</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span>{item.employee.firstName} {item.employee.lastName}</span>
                          <Badge variant="outline">{item.shiftType}</Badge>
                        </div>
                      </div>
                    ))}
                    {previewData.length > 14 && (
                      <div className="text-center text-sm text-gray-500 py-2">
                        ... and {previewData.length - 14} more schedules
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
