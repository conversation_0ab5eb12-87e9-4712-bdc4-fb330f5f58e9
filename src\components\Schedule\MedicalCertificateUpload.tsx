import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  X, 
  Download,
  Eye,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { MedicalCertificate } from '@/types/schedule';

const certificateSchema = z.object({
  doctorName: z.string().min(1, 'Doctor name is required'),
  hospitalClinic: z.string().min(1, 'Hospital/clinic name is required'),
  diagnosis: z.string().optional(),
  certificateNumber: z.string().optional(),
  issueDate: z.string().min(1, 'Issue date is required'),
});

type CertificateFormData = z.infer<typeof certificateSchema>;

interface MedicalCertificateUploadProps {
  leaveRequestId: string;
  existingCertificate?: MedicalCertificate;
  onUploadSuccess?: (certificate: MedicalCertificate) => void;
  readOnly?: boolean;
}

export function MedicalCertificateUpload({ 
  leaveRequestId, 
  existingCertificate,
  onUploadSuccess,
  readOnly = false
}: MedicalCertificateUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { toast } = useToast();

  const form = useForm<CertificateFormData>({
    resolver: zodResolver(certificateSchema),
    defaultValues: {
      doctorName: existingCertificate?.doctorName || '',
      hospitalClinic: existingCertificate?.hospitalClinic || '',
      diagnosis: existingCertificate?.diagnosis || '',
      certificateNumber: existingCertificate?.certificateNumber || '',
      issueDate: existingCertificate?.issueDate || '',
    },
  });

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid File Type",
        description: "Please upload a PDF, JPEG, or PNG file",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please upload a file smaller than 5MB",
        variant: "destructive",
      });
      return;
    }

    setUploadedFile(file);
  }, [toast]);

  const uploadFileToStorage = async (file: File): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${leaveRequestId}_${Date.now()}.${fileExt}`;
      const filePath = `medical-certificates/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('documents')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      return filePath;
    } catch (error) {
      console.error('Error uploading file:', error);
      return null;
    }
  };

  const handleSubmit = async (data: CertificateFormData) => {
    if (!uploadedFile && !existingCertificate) {
      toast({
        title: "File Required",
        description: "Please upload a medical certificate file",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      let filePath = existingCertificate?.filePath;

      // Upload new file if provided
      if (uploadedFile) {
        setUploadProgress(30);
        filePath = await uploadFileToStorage(uploadedFile);
        if (!filePath) {
          throw new Error('Failed to upload file');
        }
        setUploadProgress(60);
      }

      // Save certificate data to database
      const certificateData = {
        leave_request_id: leaveRequestId,
        doctor_name: data.doctorName,
        hospital_clinic: data.hospitalClinic,
        diagnosis: data.diagnosis || null,
        certificate_number: data.certificateNumber || null,
        issue_date: data.issueDate,
        file_path: filePath,
        verified: false,
      };

      setUploadProgress(80);

      let result;
      if (existingCertificate) {
        // Update existing certificate
        const { data: updatedCert, error } = await supabase
          .from('medical_certificates')
          .update(certificateData)
          .eq('id', existingCertificate.id)
          .select()
          .single();

        if (error) throw error;
        result = updatedCert;
      } else {
        // Create new certificate
        const { data: newCert, error } = await supabase
          .from('medical_certificates')
          .insert(certificateData)
          .select()
          .single();

        if (error) throw error;
        result = newCert;
      }

      setUploadProgress(100);

      toast({
        title: "Success",
        description: "Medical certificate uploaded successfully",
      });

      if (onUploadSuccess) {
        onUploadSuccess(result);
      }

      // Reset form
      setUploadedFile(null);
      setUploadProgress(0);
    } catch (error) {
      console.error('Error saving certificate:', error);
      toast({
        title: "Error",
        description: "Failed to save medical certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const downloadCertificate = async () => {
    if (!existingCertificate?.filePath) return;

    try {
      const { data, error } = await supabase.storage
        .from('documents')
        .download(existingCertificate.filePath);

      if (error) throw error;

      // Create download link
      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = `medical_certificate_${existingCertificate.certificateNumber || 'document'}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download certificate",
        variant: "destructive",
      });
    }
  };

  const viewCertificate = async () => {
    if (!existingCertificate?.filePath) return;

    try {
      const { data, error } = await supabase.storage
        .from('documents')
        .createSignedUrl(existingCertificate.filePath, 3600); // 1 hour expiry

      if (error) throw error;

      window.open(data.signedUrl, '_blank');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to view certificate",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Medical Certificate</span>
          {existingCertificate?.verified && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Verified
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Certificate Display */}
        {existingCertificate && (
          <div className="p-4 bg-gray-50 rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Uploaded Certificate</h4>
              <div className="flex space-x-2">
                <Button size="sm" variant="outline" onClick={viewCertificate}>
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" onClick={downloadCertificate}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Doctor:</span>
                <p className="font-medium">{existingCertificate.doctorName}</p>
              </div>
              <div>
                <span className="text-gray-600">Hospital/Clinic:</span>
                <p className="font-medium">{existingCertificate.hospitalClinic}</p>
              </div>
              <div>
                <span className="text-gray-600">Issue Date:</span>
                <p className="font-medium">{new Date(existingCertificate.issueDate).toLocaleDateString('id-ID')}</p>
              </div>
              <div>
                <span className="text-gray-600">Certificate Number:</span>
                <p className="font-medium">{existingCertificate.certificateNumber || 'N/A'}</p>
              </div>
            </div>
            
            {existingCertificate.diagnosis && (
              <div>
                <span className="text-gray-600">Diagnosis:</span>
                <p className="text-sm mt-1">{existingCertificate.diagnosis}</p>
              </div>
            )}

            {existingCertificate.verified && existingCertificate.verifiedBy && (
              <div className="flex items-center space-x-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>Verified by {existingCertificate.verifiedBy} on {new Date(existingCertificate.verifiedAt || '').toLocaleDateString('id-ID')}</span>
              </div>
            )}
          </div>
        )}

        {/* Upload Form */}
        {!readOnly && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              {/* File Upload */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Certificate File</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {uploadedFile ? (
                    <div className="space-y-2">
                      <FileText className="h-8 w-8 mx-auto text-green-500" />
                      <p className="text-sm font-medium">{uploadedFile.name}</p>
                      <p className="text-xs text-gray-500">
                        {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setUploadedFile(null)}
                      >
                        <X className="h-4 w-4 mr-1" />
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="h-8 w-8 mx-auto text-gray-400" />
                      <div>
                        <label htmlFor="certificate-upload" className="cursor-pointer">
                          <span className="text-blue-600 hover:text-blue-500">Click to upload</span>
                          <span className="text-gray-500"> or drag and drop</span>
                        </label>
                        <input
                          id="certificate-upload"
                          type="file"
                          className="hidden"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleFileSelect}
                        />
                      </div>
                      <p className="text-xs text-gray-500">
                        PDF, JPEG, or PNG files up to 5MB
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Certificate Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="doctorName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Doctor Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Dr. John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="hospitalClinic"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hospital/Clinic *</FormLabel>
                      <FormControl>
                        <Input placeholder="General Hospital" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="issueDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Issue Date *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="certificateNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Certificate Number</FormLabel>
                      <FormControl>
                        <Input placeholder="MC-2024-001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="diagnosis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Diagnosis (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Brief description of the medical condition..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              {/* Submit Button */}
              <Button 
                type="submit" 
                disabled={isUploading}
                className="w-full"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    {existingCertificate ? 'Update Certificate' : 'Upload Certificate'}
                  </>
                )}
              </Button>
            </form>
          </Form>
        )}

        {/* Requirements Notice */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Requirements:</strong> Medical certificates must be issued by a licensed medical practitioner 
            and include the doctor's name, hospital/clinic details, and issue date. The certificate will be 
            verified by HR before the sick leave is approved.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
