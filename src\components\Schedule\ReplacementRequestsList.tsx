import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader2,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ScheduleReplacement } from '@/types/schedule';
import { useAuth } from '@/hooks/useAuth';

interface ReplacementRequestsListProps {
  onRefresh?: () => void;
}

export function ReplacementRequestsList({ onRefresh }: ReplacementRequestsListProps) {
  const [replacements, setReplacements] = useState<ScheduleReplacement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    loadReplacements();
  }, []);

  const loadReplacements = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('schedule_replacements')
        .select(`
          *,
          original_schedule:schedules!original_schedule_id (
            *,
            employee:employees!employee_id (
              first_name,
              last_name,
              department,
              position
            )
          ),
          replacement_employee:employees!replacement_employee_id (
            first_name,
            last_name,
            department,
            position
          ),
          requester:employees!requested_by (
            first_name,
            last_name
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setReplacements(data || []);
    } catch (error) {
      console.error('Error loading replacements:', error);
      toast({
        title: "Error",
        description: "Failed to load replacement requests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveReplacement = async (replacementId: string) => {
    setProcessingId(replacementId);
    try {
      // Get current user's employee record
      const { data: currentEmployee, error: empError } = await supabase
        .from('employees')
        .select('id')
        .eq('email', user?.email)
        .single();

      if (empError) throw empError;

      // Update replacement status
      const { error: updateError } = await supabase
        .from('schedule_replacements')
        .update({
          status: 'approved',
          approved_by: currentEmployee.id,
        })
        .eq('id', replacementId);

      if (updateError) throw updateError;

      // Get the replacement details
      const replacement = replacements.find(r => r.id === replacementId);
      if (replacement) {
        // Update the original schedule with new employee
        const { error: scheduleError } = await supabase
          .from('schedules')
          .update({
            employee_id: replacement.replacementEmployeeId,
            notes: `Replaced by ${replacement.replacementEmployee?.firstName} ${replacement.replacementEmployee?.lastName}. Original: ${replacement.originalSchedule?.employee?.firstName} ${replacement.originalSchedule?.employee?.lastName}`,
          })
          .eq('id', replacement.originalScheduleId);

        if (scheduleError) throw scheduleError;

        // Send notifications
        await Promise.all([
          // Notify replacement employee
          supabase.from('notifications').insert({
            recipient_id: replacement.replacementEmployeeId,
            title: 'Replacement Request Approved',
            message: `Your replacement request has been approved. You are now scheduled for ${new Date(replacement.originalSchedule?.shiftDate || '').toLocaleDateString('id-ID')}`,
            type: 'replacement_approved',
            related_id: replacement.originalScheduleId,
          }),
          // Notify original employee
          supabase.from('notifications').insert({
            recipient_id: replacement.originalSchedule?.employeeId,
            title: 'Schedule Replacement Approved',
            message: `Your schedule replacement has been approved. ${replacement.replacementEmployee?.firstName} ${replacement.replacementEmployee?.lastName} will cover your shift.`,
            type: 'replacement_approved',
            related_id: replacement.originalScheduleId,
          }),
        ]);
      }

      toast({
        title: "Success",
        description: "Replacement request approved successfully",
      });

      loadReplacements();
      if (onRefresh) onRefresh();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve replacement request",
        variant: "destructive",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectReplacement = async (replacementId: string) => {
    setProcessingId(replacementId);
    try {
      // Get current user's employee record
      const { data: currentEmployee, error: empError } = await supabase
        .from('employees')
        .select('id')
        .eq('email', user?.email)
        .single();

      if (empError) throw empError;

      // Update replacement status
      const { error: updateError } = await supabase
        .from('schedule_replacements')
        .update({
          status: 'rejected',
          approved_by: currentEmployee.id,
        })
        .eq('id', replacementId);

      if (updateError) throw updateError;

      // Get the replacement details for notifications
      const replacement = replacements.find(r => r.id === replacementId);
      if (replacement) {
        // Send notifications
        await Promise.all([
          // Notify replacement employee
          supabase.from('notifications').insert({
            recipient_id: replacement.replacementEmployeeId,
            title: 'Replacement Request Rejected',
            message: `Your replacement request for ${new Date(replacement.originalSchedule?.shiftDate || '').toLocaleDateString('id-ID')} has been rejected.`,
            type: 'replacement_rejected',
            related_id: replacement.originalScheduleId,
          }),
          // Notify requester if different from replacement employee
          replacement.requestedBy !== replacement.replacementEmployeeId ? 
            supabase.from('notifications').insert({
              recipient_id: replacement.requestedBy,
              title: 'Replacement Request Rejected',
              message: `The replacement request you submitted has been rejected.`,
              type: 'replacement_rejected',
              related_id: replacement.originalScheduleId,
            }) : null,
        ].filter(Boolean));
      }

      toast({
        title: "Success",
        description: "Replacement request rejected",
      });

      loadReplacements();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject replacement request",
        variant: "destructive",
      });
    } finally {
      setProcessingId(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <AlertTriangle className="h-4 w-4" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const pendingReplacements = replacements.filter(r => r.status === 'pending');
  const processedReplacements = replacements.filter(r => r.status !== 'pending');

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>Schedule Replacement Requests</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pending" className="space-y-4">
          <TabsList>
            <TabsTrigger value="pending" className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Pending ({pendingReplacements.length})</span>
            </TabsTrigger>
            <TabsTrigger value="processed" className="flex items-center space-x-2">
              <Eye className="h-4 w-4" />
              <span>Processed ({processedReplacements.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending">
            <div className="space-y-4">
              {pendingReplacements.length === 0 ? (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No pending replacement requests.
                  </AlertDescription>
                </Alert>
              ) : (
                pendingReplacements.map((replacement) => (
                  <Card key={replacement.id} className="border-l-4 border-l-yellow-500">
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(replacement.status)}
                            <Badge className={getStatusColor(replacement.status)}>
                              {replacement.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(replacement.createdAt || '').toLocaleDateString('id-ID')}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium text-sm text-gray-600">Original Employee</h4>
                            <p className="font-semibold">
                              {replacement.originalSchedule?.employee?.firstName} {replacement.originalSchedule?.employee?.lastName}
                            </p>
                            <p className="text-sm text-gray-600">
                              {replacement.originalSchedule?.employee?.department}
                            </p>
                          </div>
                          <div>
                            <h4 className="font-medium text-sm text-gray-600">Replacement Employee</h4>
                            <p className="font-semibold">
                              {replacement.replacementEmployee?.firstName} {replacement.replacementEmployee?.lastName}
                            </p>
                            <p className="text-sm text-gray-600">
                              {replacement.replacementEmployee?.department}
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">
                              {new Date(replacement.originalSchedule?.shiftDate || '').toLocaleDateString('id-ID')}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">
                              {replacement.originalSchedule?.startTime} - {replacement.originalSchedule?.endTime}
                            </span>
                          </div>
                          <div>
                            <Badge variant="outline">
                              {replacement.originalSchedule?.shiftType}
                            </Badge>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm text-gray-600">Reason</h4>
                          <p className="text-sm">{replacement.reason}</p>
                          {replacement.notes && (
                            <>
                              <h4 className="font-medium text-sm text-gray-600 mt-2">Notes</h4>
                              <p className="text-sm text-gray-600">{replacement.notes}</p>
                            </>
                          )}
                        </div>

                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRejectReplacement(replacement.id)}
                            disabled={processingId === replacement.id}
                          >
                            {processingId === replacement.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <XCircle className="h-4 w-4" />
                            )}
                            Reject
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleApproveReplacement(replacement.id)}
                            disabled={processingId === replacement.id}
                          >
                            {processingId === replacement.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <CheckCircle className="h-4 w-4" />
                            )}
                            Approve
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="processed">
            <div className="space-y-4">
              {processedReplacements.length === 0 ? (
                <Alert>
                  <Eye className="h-4 w-4" />
                  <AlertDescription>
                    No processed replacement requests.
                  </AlertDescription>
                </Alert>
              ) : (
                processedReplacements.map((replacement) => (
                  <Card key={replacement.id} className={`border-l-4 ${
                    replacement.status === 'approved' ? 'border-l-green-500' : 
                    replacement.status === 'rejected' ? 'border-l-red-500' : 'border-l-blue-500'
                  }`}>
                    <CardContent className="pt-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(replacement.status)}
                            <Badge className={getStatusColor(replacement.status)}>
                              {replacement.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(replacement.createdAt || '').toLocaleDateString('id-ID')}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium text-sm text-gray-600">Original → Replacement</h4>
                            <p className="text-sm">
                              {replacement.originalSchedule?.employee?.firstName} {replacement.originalSchedule?.employee?.lastName}
                              {' → '}
                              {replacement.replacementEmployee?.firstName} {replacement.replacementEmployee?.lastName}
                            </p>
                          </div>
                          <div>
                            <h4 className="font-medium text-sm text-gray-600">Schedule</h4>
                            <p className="text-sm">
                              {new Date(replacement.originalSchedule?.shiftDate || '').toLocaleDateString('id-ID')} 
                              {' '}
                              ({replacement.originalSchedule?.startTime} - {replacement.originalSchedule?.endTime})
                            </p>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-sm text-gray-600">Reason</h4>
                          <p className="text-sm">{replacement.reason}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
