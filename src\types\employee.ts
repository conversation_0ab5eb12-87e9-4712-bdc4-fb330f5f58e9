
export interface Employee {
  id: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: EmployeeRole;
  department: Department;
  position: string;
  joinDate: string;
  status: EmployeeStatus;
  shift: Shift;
  salary?: number;
  avatar?: string;
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  certifications?: string[];
  skills?: string[];
}

export type EmployeeRole = 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';

export type Department = 
  | 'emergency'
  | 'surgery' 
  | 'pediatrics'
  | 'cardiology'
  | 'orthopedics'
  | 'pharmacy'
  | 'laboratory'
  | 'radiology'
  | 'administration'
  | 'maintenance';

export type EmployeeStatus = 'active' | 'inactive' | 'on_leave' | 'terminated';

export type Shift = 'morning' | 'afternoon' | 'night' | 'rotating';

export interface EmployeeFilters {
  role?: EmployeeRole;
  department?: Department;
  status?: EmployeeStatus;
  shift?: Shift;
  search?: string;
}
