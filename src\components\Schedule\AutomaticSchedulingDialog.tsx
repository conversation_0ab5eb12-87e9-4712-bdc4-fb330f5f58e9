import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Calendar, Users, AlertTriangle, CheckCircle, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { automaticSchedulingService, SchedulingRequest, SchedulingResult } from '@/services/automaticScheduling';
import { useEmployees } from '@/hooks/useEmployees';

const schedulingSchema = z.object({
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  department: z.string().optional(),
  overrideExisting: z.boolean().default(false),
});

type SchedulingFormData = z.infer<typeof schedulingSchema>;

interface AutomaticSchedulingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function AutomaticSchedulingDialog({ 
  open, 
  onOpenChange, 
  onSuccess 
}: AutomaticSchedulingDialogProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<SchedulingResult | null>(null);
  const { toast } = useToast();
  const { employees } = useEmployees();

  const form = useForm<SchedulingFormData>({
    resolver: zodResolver(schedulingSchema),
    defaultValues: {
      startDate: '',
      endDate: '',
      department: '',
      overrideExisting: false,
    },
  });

  const departments = Array.from(new Set(employees.map(emp => emp.department)));

  const handleGenerateSchedules = async (data: SchedulingFormData) => {
    setIsGenerating(true);
    setResult(null);

    try {
      const request: SchedulingRequest = {
        startDate: data.startDate,
        endDate: data.endDate,
        department: data.department || undefined,
        overrideExisting: data.overrideExisting,
      };

      const schedulingResult = await automaticSchedulingService.generateSchedules(request);
      setResult(schedulingResult);

      if (schedulingResult.success) {
        toast({
          title: "Schedules Generated Successfully",
          description: `Created ${schedulingResult.schedulesCreated} schedules`,
        });
        
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: "Scheduling Failed",
          description: "There were issues generating the schedules. Check the results below.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate schedules. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = () => {
    setResult(null);
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Wand2 className="h-5 w-5" />
            <span>Automatic Schedule Generation</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleGenerateSchedules)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department (Optional)</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select department or leave empty for all" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="">All Departments</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept} value={dept}>
                            {dept.charAt(0).toUpperCase() + dept.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="overrideExisting"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Override Existing Schedules</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Replace any existing schedules in the selected date range
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Generating Schedules...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 mr-2" />
                    Generate Schedules
                  </>
                )}
              </Button>
            </form>
          </Form>

          {/* Results Section */}
          {result && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                )}
                <h3 className="text-lg font-semibold">
                  {result.success ? 'Generation Successful' : 'Generation Failed'}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Schedules Created</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-blue-500" />
                      <span className="text-2xl font-bold">{result.schedulesCreated}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Conflicts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span className="text-2xl font-bold">{result.conflicts.length}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Warnings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      <span className="text-2xl font-bold">{result.warnings.length}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Conflicts */}
              {result.conflicts.length > 0 && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Conflicts encountered:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {result.conflicts.map((conflict, index) => (
                          <li key={index} className="text-sm">{conflict}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Warnings */}
              {result.warnings.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Warnings:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {result.warnings.map((warning, index) => (
                          <li key={index} className="text-sm">{warning}</li>
                        ))}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Success message */}
              {result.success && result.schedulesCreated > 0 && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Successfully generated {result.schedulesCreated} schedules. 
                    The schedules have been saved and are now visible in the calendar view.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
