import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Notification } from '@/types/schedule';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';

export function useNotifications() {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [currentEmployeeId, setCurrentEmployeeId] = useState<string | null>(null);

  // Get current employee ID
  useEffect(() => {
    const getCurrentEmployee = async () => {
      if (user?.email) {
        const { data, error } = await supabase
          .from('employees')
          .select('id')
          .eq('email', user.email)
          .single();
        
        if (!error && data) {
          setCurrentEmployeeId(data.id);
        }
      }
    };

    getCurrentEmployee();
  }, [user]);

  // Fetch notifications
  const { data: notifications = [], isLoading: isLoadingNotifications } = useQuery({
    queryKey: ['notifications', currentEmployeeId],
    queryFn: async () => {
      if (!currentEmployeeId) return [];
      
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('recipient_id', currentEmployeeId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      return data || [];
    },
    enabled: !!currentEmployeeId,
  });

  // Get unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Mark notification as read
  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to mark notification as read",
        variant: "destructive",
      });
    },
  });

  // Mark all notifications as read
  const markAllAsReadMutation = useMutation({
    mutationFn: async () => {
      if (!currentEmployeeId) return;
      
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('recipient_id', currentEmployeeId)
        .eq('read', false);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: "Success",
        description: "All notifications marked as read",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read",
        variant: "destructive",
      });
    },
  });

  // Delete notification
  const deleteNotificationMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete notification",
        variant: "destructive",
      });
    },
  });

  // Create notification (for system use)
  const createNotificationMutation = useMutation({
    mutationFn: async (notification: {
      recipientId: string;
      title: string;
      message: string;
      type: string;
      relatedId?: string;
    }) => {
      const { error } = await supabase
        .from('notifications')
        .insert({
          recipient_id: notification.recipientId,
          title: notification.title,
          message: notification.message,
          type: notification.type,
          related_id: notification.relatedId,
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });

  // Set up real-time subscription for notifications
  useEffect(() => {
    if (!currentEmployeeId) return;

    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `recipient_id=eq.${currentEmployeeId}`,
        },
        (payload) => {
          const newNotification = payload.new as Notification;
          
          // Show toast for new notification
          toast({
            title: newNotification.title,
            description: newNotification.message,
            duration: 5000,
          });

          // Invalidate queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['notifications'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [currentEmployeeId, queryClient, toast]);

  // Utility function to send notification to multiple recipients
  const sendNotificationToMultiple = async (
    recipientIds: string[],
    title: string,
    message: string,
    type: string,
    relatedId?: string
  ) => {
    const notifications = recipientIds.map(recipientId => ({
      recipient_id: recipientId,
      title,
      message,
      type,
      related_id: relatedId,
    }));

    const { error } = await supabase
      .from('notifications')
      .insert(notifications);

    if (error) {
      console.error('Error sending notifications:', error);
      throw error;
    }

    queryClient.invalidateQueries({ queryKey: ['notifications'] });
  };

  // Utility function to send schedule change notification
  const sendScheduleChangeNotification = async (
    employeeIds: string[],
    scheduleDate: string,
    changeType: 'created' | 'updated' | 'cancelled' | 'replaced',
    details?: string
  ) => {
    const titles = {
      created: 'New Schedule Assigned',
      updated: 'Schedule Updated',
      cancelled: 'Schedule Cancelled',
      replaced: 'Schedule Replaced',
    };

    const messages = {
      created: `You have been assigned a new schedule for ${new Date(scheduleDate).toLocaleDateString('id-ID')}`,
      updated: `Your schedule for ${new Date(scheduleDate).toLocaleDateString('id-ID')} has been updated`,
      cancelled: `Your schedule for ${new Date(scheduleDate).toLocaleDateString('id-ID')} has been cancelled`,
      replaced: `Your schedule for ${new Date(scheduleDate).toLocaleDateString('id-ID')} has been replaced`,
    };

    const message = details ? `${messages[changeType]}. ${details}` : messages[changeType];

    await sendNotificationToMultiple(
      employeeIds,
      titles[changeType],
      message,
      'schedule_change'
    );
  };

  return {
    notifications,
    unreadCount,
    isLoadingNotifications,
    markAsRead: markAsReadMutation.mutate,
    markAllAsRead: markAllAsReadMutation.mutate,
    deleteNotification: deleteNotificationMutation.mutate,
    createNotification: createNotificationMutation.mutate,
    sendNotificationToMultiple,
    sendScheduleChangeNotification,
    isMarkingAsRead: markAsReadMutation.isPending,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
    isDeletingNotification: deleteNotificationMutation.isPending,
    isCreatingNotification: createNotificationMutation.isPending,
  };
}
