
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, List, User, Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import { Schedule } from '@/types/schedule';
import { useEmployees } from '@/hooks/useEmployees';

interface EmployeeScheduleViewProps {
  schedules: Schedule[];
}

export function EmployeeScheduleView({ schedules }: EmployeeScheduleViewProps) {
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>('');
  const [currentDate, setCurrentDate] = useState(new Date());
  const { employees } = useEmployees();

  const selectedEmployee = employees.find(emp => emp.id === selectedEmployeeId);
  const employeeSchedules = schedules.filter(schedule => 
    schedule.employeeId === selectedEmployeeId
  );

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const getScheduleForDate = (date: Date | null) => {
    if (!date) return null;
    const dateString = date.toISOString().split('T')[0];
    return employeeSchedules.find(schedule => schedule.shiftDate === dateString);
  };

  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1));
  };

  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

  const getShiftTypeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-500';
      case 'afternoon': return 'bg-blue-500';
      case 'night': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getShiftTypeBadgeColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const days = getDaysInMonth(currentDate);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <User className="h-5 w-5" />
          <span>Jadwal Karyawan</span>
        </CardTitle>
        
        <div className="space-y-4">
          <Select onValueChange={setSelectedEmployeeId} value={selectedEmployeeId}>
            <SelectTrigger>
              <SelectValue placeholder="Pilih karyawan untuk melihat jadwal" />
            </SelectTrigger>
            <SelectContent>
              {employees.map((employee) => (
                <SelectItem key={employee.id} value={employee.id}>
                  {employee.firstName} {employee.lastName} - {employee.department}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      {selectedEmployeeId && (
        <CardContent>
          <div className="mb-4">
            <h3 className="text-lg font-semibold">
              {selectedEmployee?.firstName} {selectedEmployee?.lastName}
            </h3>
            <p className="text-gray-600">
              {selectedEmployee?.department} - {selectedEmployee?.position}
            </p>
          </div>

          <Tabs defaultValue="calendar" className="space-y-4">
            <TabsList>
              <TabsTrigger value="calendar" className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>Kalender</span>
              </TabsTrigger>
              <TabsTrigger value="list" className="flex items-center space-x-2">
                <List className="h-4 w-4" />
                <span>Daftar</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="calendar">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <h3 className="text-lg font-semibold">
                    {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </h3>
                  <Button variant="outline" size="sm" onClick={goToNextMonth}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-7 gap-1 mb-4">
                  {dayNames.map((day) => (
                    <div key={day} className="p-2 text-center font-medium text-gray-600 text-sm">
                      {day}
                    </div>
                  ))}
                </div>
                
                <div className="grid grid-cols-7 gap-1">
                  {days.map((day, index) => {
                    const daySchedule = getScheduleForDate(day);
                    const isToday = day && day.toDateString() === new Date().toDateString();
                    
                    return (
                      <div
                        key={index}
                        className={`min-h-[80px] p-2 border rounded ${
                          day ? 'bg-white' : 'bg-gray-50'
                        } ${isToday ? 'ring-2 ring-blue-500' : ''}`}
                      >
                        {day && (
                          <>
                            <div className={`text-sm font-medium mb-1 ${
                              isToday ? 'text-blue-600' : 'text-gray-900'
                            }`}>
                              {day.getDate()}
                            </div>
                            {daySchedule && (
                              <div className="space-y-1">
                                <div
                                  className={`w-full h-2 rounded ${getShiftTypeColor(daySchedule.shiftType)}`}
                                  title={`${daySchedule.shiftType} - ${daySchedule.startTime}-${daySchedule.endTime}`}
                                />
                                <div className="text-xs text-gray-600">
                                  {daySchedule.startTime}-{daySchedule.endTime}
                                </div>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="list">
              <div className="space-y-3">
                {employeeSchedules.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Tidak ada jadwal untuk karyawan ini</p>
                  </div>
                ) : (
                  employeeSchedules
                    .sort((a, b) => new Date(a.shiftDate).getTime() - new Date(b.shiftDate).getTime())
                    .map((schedule) => (
                      <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium">
                              {new Date(schedule.shiftDate).toLocaleDateString('id-ID', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </p>
                            <p className="text-sm text-gray-600">
                              {schedule.startTime} - {schedule.endTime}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getShiftTypeBadgeColor(schedule.shiftType)}>
                            {schedule.shiftType === 'morning' ? 'Pagi' : 
                             schedule.shiftType === 'afternoon' ? 'Siang' : 
                             schedule.shiftType === 'night' ? 'Malam' : schedule.shiftType}
                          </Badge>
                          <Badge variant={schedule.status === 'completed' ? 'default' : 'secondary'}>
                            {schedule.status === 'scheduled' ? 'Terjadwal' :
                             schedule.status === 'completed' ? 'Selesai' :
                             schedule.status === 'cancelled' ? 'Dibatalkan' :
                             schedule.status === 'no_show' ? 'Tidak Hadir' : schedule.status}
                          </Badge>
                        </div>
                      </div>
                    ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      )}
    </Card>
  );
}
