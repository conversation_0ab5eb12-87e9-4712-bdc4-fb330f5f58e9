
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Role, Department, Position } from '@/types/management';
import { useToast } from '@/hooks/use-toast';

export function useManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Roles
  const { data: roles = [], isLoading: isLoadingRoles } = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      
      return data.map((role: any): Role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: role.permissions,
        isActive: role.is_active,
        createdAt: role.created_at,
        updatedAt: role.updated_at,
      }));
    },
  });

  // Departments
  const { data: departments = [], isLoading: isLoadingDepartments } = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('departments')
        .select(`
          *,
          employees!departments_manager_id_fkey(
            first_name,
            last_name
          )
        `)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      
      return data.map((dept: any): Department => ({
        id: dept.id,
        name: dept.name,
        description: dept.description,
        managerId: dept.manager_id,
        isActive: dept.is_active,
        createdAt: dept.created_at,
        updatedAt: dept.updated_at,
        manager: dept.employees ? {
          firstName: dept.employees.first_name,
          lastName: dept.employees.last_name,
        } : undefined,
      }));
    },
  });

  // Positions
  const { data: positions = [], isLoading: isLoadingPositions } = useQuery({
    queryKey: ['positions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('positions')
        .select(`
          *,
          departments!inner(name),
          roles!inner(name)
        `)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      
      return data.map((pos: any): Position => ({
        id: pos.id,
        name: pos.name,
        description: pos.description,
        departmentId: pos.department_id,
        roleId: pos.role_id,
        isActive: pos.is_active,
        createdAt: pos.created_at,
        updatedAt: pos.updated_at,
        department: pos.departments ? {
          name: pos.departments.name,
        } : undefined,
        role: pos.roles ? {
          name: pos.roles.name,
        } : undefined,
      }));
    },
  });

  // Mutations for roles
  const createRoleMutation = useMutation({
    mutationFn: async (roleData: Omit<Role, 'id'>) => {
      const { data, error } = await supabase
        .from('roles')
        .insert({
          name: roleData.name,
          description: roleData.description,
          permissions: roleData.permissions,
          is_active: roleData.isActive,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: "Berhasil",
        description: "Role berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutations for departments
  const createDepartmentMutation = useMutation({
    mutationFn: async (deptData: Omit<Department, 'id'>) => {
      const { data, error } = await supabase
        .from('departments')
        .insert({
          name: deptData.name,
          description: deptData.description,
          manager_id: deptData.managerId,
          is_active: deptData.isActive,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      toast({
        title: "Berhasil",
        description: "Departemen berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutations for positions
  const createPositionMutation = useMutation({
    mutationFn: async (posData: Omit<Position, 'id'>) => {
      const { data, error } = await supabase
        .from('positions')
        .insert({
          name: posData.name,
          description: posData.description,
          department_id: posData.departmentId,
          role_id: posData.roleId,
          is_active: posData.isActive,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['positions'] });
      toast({
        title: "Berhasil",
        description: "Posisi berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    roles,
    departments,
    positions,
    isLoadingRoles,
    isLoadingDepartments,
    isLoadingPositions,
    createRole: createRoleMutation.mutate,
    createDepartment: createDepartmentMutation.mutate,
    createPosition: createPositionMutation.mutate,
    isCreatingRole: createRoleMutation.isPending,
    isCreatingDepartment: createDepartmentMutation.isPending,
    isCreatingPosition: createPositionMutation.isPending,
  };
}
