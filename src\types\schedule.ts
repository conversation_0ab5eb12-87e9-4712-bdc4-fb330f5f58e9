
export interface Schedule {
  id: string;
  employeeId: string;
  shiftDate: string;
  shiftType: 'morning' | 'afternoon' | 'night' | 'rotating';
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data from employees table
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
}

export interface LeaveRequest {
  id: string;
  employeeId: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data from employees table
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
}

export interface ScheduleFilters {
  date?: string;
  employeeId?: string;
  shiftType?: 'morning' | 'afternoon' | 'night' | 'rotating';
  status?: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  department?: string;
}
