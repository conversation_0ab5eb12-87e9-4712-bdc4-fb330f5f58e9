
export interface Schedule {
  id: string;
  employeeId: string;
  shiftDate: string;
  shiftType: 'morning' | 'afternoon' | 'night' | 'rotating';
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data from employees table
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
}

export interface LeaveRequest {
  id: string;
  employeeId: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data from employees table
  employee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
  // Enhanced fields for approval workflow
  approvals?: LeaveApproval[];
  medicalCertificate?: MedicalCertificate;
  totalDays?: number;
  currentApprovalLevel?: number;
  nextApproverId?: string;
}

export interface ScheduleFilters {
  date?: string;
  employeeId?: string;
  shiftType?: 'morning' | 'afternoon' | 'night' | 'rotating';
  status?: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  department?: string;
}

export interface EmployeeShiftPreference {
  id: string;
  employeeId: string;
  preferredShiftType: 'morning' | 'afternoon' | 'night' | 'rotating';
  preferredDaysOfWeek: number[]; // 0=Sunday, 1=Monday, etc.
  maxConsecutiveDays: number;
  minRestHours: number;
  overtimeWilling: boolean;
  weekendWilling: boolean;
  nightShiftWilling: boolean;
  priorityLevel: number; // 1=low, 2=medium, 3=high
  effectiveFrom: string;
  effectiveUntil?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ScheduleTemplate {
  id: string;
  name: string;
  department: string;
  shiftType: 'morning' | 'afternoon' | 'night' | 'rotating';
  startTime: string;
  endTime: string;
  requiredStaffCount: number;
  requiredRoles: string[];
  daysOfWeek: number[];
  isActive: boolean;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SchedulingRule {
  id: string;
  ruleName: string;
  ruleType: 'fairness' | 'coverage' | 'preference' | 'constraint';
  department?: string;
  priority: number;
  ruleConfig: Record<string, any>;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ScheduleReplacement {
  id: string;
  originalScheduleId: string;
  replacementEmployeeId: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  requestedBy: string;
  approvedBy?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data
  originalSchedule?: Schedule;
  replacementEmployee?: {
    firstName: string;
    lastName: string;
    department: string;
    position: string;
  };
  requester?: {
    firstName: string;
    lastName: string;
  };
}

export interface LeaveBalance {
  id: string;
  employeeId: string;
  year: number;
  annualLeaveTotal: number;
  annualLeaveUsed: number;
  annualLeaveRemaining: number;
  sickLeaveUsed: number;
  emergencyLeaveUsed: number;
  maternityLeaveUsed: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface LeaveApproval {
  id: string;
  leaveRequestId: string;
  approverId: string;
  approvalLevel: number; // 1: Direct supervisor, 2: Department head, 3: HR
  status: 'pending' | 'approved' | 'rejected';
  comments?: string;
  approvedAt?: string;
  createdAt?: string;
  updatedAt?: string;
  // Joined data
  approver?: {
    firstName: string;
    lastName: string;
    position: string;
  };
}

export interface MedicalCertificate {
  id: string;
  leaveRequestId: string;
  doctorName: string;
  hospitalClinic: string;
  diagnosis?: string;
  certificateNumber?: string;
  issueDate: string;
  filePath?: string;
  verified: boolean;
  verifiedBy?: string;
  verifiedAt?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface Notification {
  id: string;
  recipientId: string;
  title: string;
  message: string;
  type: string; // schedule_change, leave_approved, leave_rejected, replacement_request, etc.
  relatedId?: string;
  read: boolean;
  createdAt?: string;
}
