import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Settings, Clock, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { EmployeeShiftPreference } from '@/types/schedule';
import { useEmployees } from '@/hooks/useEmployees';

const preferencesSchema = z.object({
  employeeId: z.string().min(1, 'Employee is required'),
  preferredShiftType: z.enum(['morning', 'afternoon', 'night', 'rotating']),
  preferredDaysOfWeek: z.array(z.number()).min(1, 'Select at least one day'),
  maxConsecutiveDays: z.number().min(1).max(7),
  minRestHours: z.number().min(8).max(24),
  overtimeWilling: z.boolean(),
  weekendWilling: z.boolean(),
  nightShiftWilling: z.boolean(),
  priorityLevel: z.number().min(1).max(3),
  effectiveFrom: z.string().min(1, 'Effective date is required'),
  effectiveUntil: z.string().optional(),
});

type PreferencesFormData = z.infer<typeof preferencesSchema>;

interface ShiftPreferencesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employeeId?: string;
  onSuccess?: () => void;
}

const daysOfWeek = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
];

const shiftTypes = [
  { value: 'morning', label: 'Morning Shift' },
  { value: 'afternoon', label: 'Afternoon Shift' },
  { value: 'night', label: 'Night Shift' },
  { value: 'rotating', label: 'Rotating Shifts' },
];

const priorityLevels = [
  { value: 1, label: 'Low Priority' },
  { value: 2, label: 'Medium Priority' },
  { value: 3, label: 'High Priority' },
];

export function ShiftPreferencesDialog({ 
  open, 
  onOpenChange, 
  employeeId,
  onSuccess 
}: ShiftPreferencesDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [existingPreferences, setExistingPreferences] = useState<EmployeeShiftPreference | null>(null);
  const { toast } = useToast();
  const { employees } = useEmployees();

  const form = useForm<PreferencesFormData>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      employeeId: employeeId || '',
      preferredShiftType: 'morning',
      preferredDaysOfWeek: [1, 2, 3, 4, 5], // Weekdays
      maxConsecutiveDays: 5,
      minRestHours: 12,
      overtimeWilling: false,
      weekendWilling: true,
      nightShiftWilling: false,
      priorityLevel: 1,
      effectiveFrom: new Date().toISOString().split('T')[0],
      effectiveUntil: '',
    },
  });

  // Load existing preferences when dialog opens
  useEffect(() => {
    if (open && employeeId) {
      loadExistingPreferences(employeeId);
    }
  }, [open, employeeId]);

  const loadExistingPreferences = async (empId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('employee_shift_preferences')
        .select('*')
        .eq('employee_id', empId)
        .lte('effective_from', new Date().toISOString())
        .or('effective_until.is.null,effective_until.gte.' + new Date().toISOString())
        .order('effective_from', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (data && data.length > 0) {
        const preferences = data[0];
        setExistingPreferences(preferences);
        
        // Populate form with existing data
        form.reset({
          employeeId: empId,
          preferredShiftType: preferences.preferred_shift_type,
          preferredDaysOfWeek: preferences.preferred_days_of_week,
          maxConsecutiveDays: preferences.max_consecutive_days,
          minRestHours: preferences.min_rest_hours,
          overtimeWilling: preferences.overtime_willing,
          weekendWilling: preferences.weekend_willing,
          nightShiftWilling: preferences.night_shift_willing,
          priorityLevel: preferences.priority_level,
          effectiveFrom: preferences.effective_from,
          effectiveUntil: preferences.effective_until || '',
        });
      } else {
        form.setValue('employeeId', empId);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load existing preferences",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePreferences = async (data: PreferencesFormData) => {
    setIsSaving(true);
    try {
      const preferenceData = {
        employee_id: data.employeeId,
        preferred_shift_type: data.preferredShiftType,
        preferred_days_of_week: data.preferredDaysOfWeek,
        max_consecutive_days: data.maxConsecutiveDays,
        min_rest_hours: data.minRestHours,
        overtime_willing: data.overtimeWilling,
        weekend_willing: data.weekendWilling,
        night_shift_willing: data.nightShiftWilling,
        priority_level: data.priorityLevel,
        effective_from: data.effectiveFrom,
        effective_until: data.effectiveUntil || null,
      };

      if (existingPreferences) {
        // Update existing preferences
        const { error } = await supabase
          .from('employee_shift_preferences')
          .update(preferenceData)
          .eq('id', existingPreferences.id);

        if (error) throw error;
      } else {
        // Insert new preferences
        const { error } = await supabase
          .from('employee_shift_preferences')
          .insert(preferenceData);

        if (error) throw error;
      }

      toast({
        title: "Success",
        description: "Shift preferences saved successfully",
      });

      if (onSuccess) {
        onSuccess();
      }
      
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    form.reset();
    setExistingPreferences(null);
    onOpenChange(false);
  };

  const selectedEmployee = employees.find(emp => emp.id === form.watch('employeeId'));

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Shift Preferences</span>
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSavePreferences)} className="space-y-6">
              {/* Employee Selection */}
              {!employeeId && (
                <FormField
                  control={form.control}
                  name="employeeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employee</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select employee" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {employees.map((employee) => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.firstName} {employee.lastName} - {employee.department}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Employee Info Card */}
              {selectedEmployee && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm">Employee Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Name:</span>
                      <span className="font-medium">{selectedEmployee.firstName} {selectedEmployee.lastName}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Department:</span>
                      <Badge variant="outline">{selectedEmployee.department}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Position:</span>
                      <span className="text-sm">{selectedEmployee.position}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Current Shift:</span>
                      <Badge>{selectedEmployee.shift}</Badge>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Shift Preferences */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="preferredShiftType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Shift Type</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {shiftTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priorityLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority Level</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {priorityLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value.toString()}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Preferred Days */}
              <FormField
                control={form.control}
                name="preferredDaysOfWeek"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred Days of Week</FormLabel>
                    <div className="grid grid-cols-4 gap-2">
                      {daysOfWeek.map((day) => (
                        <div key={day.value} className="flex items-center space-x-2">
                          <Checkbox
                            checked={field.value?.includes(day.value)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, day.value]);
                              } else {
                                field.onChange(field.value?.filter(d => d !== day.value));
                              }
                            }}
                          />
                          <label className="text-sm">{day.label}</label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Constraints */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="maxConsecutiveDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Consecutive Days</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1" 
                          max="7" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minRestHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Min Rest Hours</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="8" 
                          max="24" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Willingness Checkboxes */}
              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="overtimeWilling"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Willing to work overtime</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="weekendWilling"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Willing to work weekends</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nightShiftWilling"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Willing to work night shifts</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              {/* Effective Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="effectiveFrom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Effective From</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="effectiveUntil"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Effective Until (Optional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Preferences'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
