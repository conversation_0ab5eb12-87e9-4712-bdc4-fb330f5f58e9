import { supabase } from '@/integrations/supabase/client';
import { 
  Schedule, 
  EmployeeShiftPreference, 
  ScheduleTemplate, 
  SchedulingRule,
  LeaveRequest 
} from '@/types/schedule';
import { Employee } from '@/types/employee';

export interface SchedulingRequest {
  startDate: string;
  endDate: string;
  department?: string;
  templateIds?: string[];
  overrideExisting?: boolean;
}

export interface SchedulingResult {
  success: boolean;
  schedulesCreated: number;
  conflicts: string[];
  warnings: string[];
  schedules: Schedule[];
}

export class AutomaticSchedulingService {
  
  /**
   * Main method to generate automatic schedules
   */
  async generateSchedules(request: SchedulingRequest): Promise<SchedulingResult> {
    try {
      // 1. Fetch all required data
      const [employees, preferences, templates, rules, existingSchedules, leaveRequests] = 
        await Promise.all([
          this.getActiveEmployees(request.department),
          this.getEmployeePreferences(),
          this.getScheduleTemplates(request.department, request.templateIds),
          this.getSchedulingRules(request.department),
          this.getExistingSchedules(request.startDate, request.endDate),
          this.getApprovedLeaveRequests(request.startDate, request.endDate)
        ]);

      // 2. Generate schedule proposals
      const proposedSchedules = await this.generateScheduleProposals(
        request, templates, employees, preferences, rules
      );

      // 3. Apply constraints and rules
      const validatedSchedules = await this.validateAndOptimizeSchedules(
        proposedSchedules, employees, preferences, rules, existingSchedules, leaveRequests
      );

      // 4. Save schedules to database
      const result = await this.saveSchedules(validatedSchedules, request.overrideExisting);

      return result;
    } catch (error) {
      console.error('Error generating automatic schedules:', error);
      return {
        success: false,
        schedulesCreated: 0,
        conflicts: [`Error generating schedules: ${error.message}`],
        warnings: [],
        schedules: []
      };
    }
  }

  /**
   * Get active employees for scheduling
   */
  private async getActiveEmployees(department?: string): Promise<Employee[]> {
    let query = supabase
      .from('employees')
      .select('*')
      .eq('status', 'active');

    if (department) {
      query = query.eq('department', department);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  /**
   * Get employee shift preferences
   */
  private async getEmployeePreferences(): Promise<EmployeeShiftPreference[]> {
    const { data, error } = await supabase
      .from('employee_shift_preferences')
      .select('*')
      .lte('effective_from', new Date().toISOString())
      .or('effective_until.is.null,effective_until.gte.' + new Date().toISOString());

    if (error) throw error;
    return data || [];
  }

  /**
   * Get schedule templates
   */
  private async getScheduleTemplates(department?: string, templateIds?: string[]): Promise<ScheduleTemplate[]> {
    let query = supabase
      .from('schedule_templates')
      .select('*')
      .eq('is_active', true);

    if (department) {
      query = query.eq('department', department);
    }

    if (templateIds && templateIds.length > 0) {
      query = query.in('id', templateIds);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  /**
   * Get scheduling rules
   */
  private async getSchedulingRules(department?: string): Promise<SchedulingRule[]> {
    let query = supabase
      .from('scheduling_rules')
      .select('*')
      .eq('is_active', true)
      .order('priority', { ascending: false });

    if (department) {
      query = query.or(`department.is.null,department.eq.${department}`);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  /**
   * Get existing schedules in the date range
   */
  private async getExistingSchedules(startDate: string, endDate: string): Promise<Schedule[]> {
    const { data, error } = await supabase
      .from('schedules')
      .select('*')
      .gte('shift_date', startDate)
      .lte('shift_date', endDate);

    if (error) throw error;
    return data || [];
  }

  /**
   * Get approved leave requests in the date range
   */
  private async getApprovedLeaveRequests(startDate: string, endDate: string): Promise<LeaveRequest[]> {
    const { data, error } = await supabase
      .from('leave_requests')
      .select('*')
      .eq('status', 'approved')
      .lte('start_date', endDate)
      .gte('end_date', startDate);

    if (error) throw error;
    return data || [];
  }

  /**
   * Generate initial schedule proposals based on templates
   */
  private async generateScheduleProposals(
    request: SchedulingRequest,
    templates: ScheduleTemplate[],
    employees: Employee[],
    preferences: EmployeeShiftPreference[],
    rules: SchedulingRule[]
  ): Promise<Partial<Schedule>[]> {
    const proposals: Partial<Schedule>[] = [];
    const startDate = new Date(request.startDate);
    const endDate = new Date(request.endDate);

    // Iterate through each day in the range
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dayOfWeek = date.getDay();
      const dateString = date.toISOString().split('T')[0];

      // For each template that applies to this day
      for (const template of templates) {
        if (template.daysOfWeek.includes(dayOfWeek)) {
          // Find suitable employees for this template
          const suitableEmployees = this.findSuitableEmployees(
            template, employees, preferences, dayOfWeek
          );

          // Assign employees to shifts
          const assignedEmployees = this.assignEmployeesToShift(
            template, suitableEmployees, preferences
          );

          // Create schedule proposals
          for (const employee of assignedEmployees) {
            proposals.push({
              employeeId: employee.id,
              shiftDate: dateString,
              shiftType: template.shiftType,
              startTime: template.startTime,
              endTime: template.endTime,
              status: 'scheduled',
              notes: `Auto-generated from template: ${template.name}`
            });
          }
        }
      }
    }

    return proposals;
  }

  /**
   * Find employees suitable for a specific template
   */
  private findSuitableEmployees(
    template: ScheduleTemplate,
    employees: Employee[],
    preferences: EmployeeShiftPreference[],
    dayOfWeek: number
  ): Employee[] {
    return employees.filter(employee => {
      // Check if employee's role matches template requirements
      if (!template.requiredRoles.includes(employee.role)) {
        return false;
      }

      // Check if employee's department matches template
      if (employee.department !== template.department) {
        return false;
      }

      // Check employee preferences
      const preference = preferences.find(p => p.employeeId === employee.id);
      if (preference) {
        // Check if employee prefers this day of week
        if (!preference.preferredDaysOfWeek.includes(dayOfWeek)) {
          return false;
        }

        // Check if employee is willing to work this shift type
        if (template.shiftType === 'night' && !preference.nightShiftWilling) {
          return false;
        }

        // Check if it's weekend and employee is willing
        if ((dayOfWeek === 0 || dayOfWeek === 6) && !preference.weekendWilling) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Assign employees to shifts based on preferences and fairness
   */
  private assignEmployeesToShift(
    template: ScheduleTemplate,
    suitableEmployees: Employee[],
    preferences: EmployeeShiftPreference[]
  ): Employee[] {
    // Sort employees by priority and fairness
    const sortedEmployees = suitableEmployees.sort((a, b) => {
      const prefA = preferences.find(p => p.employeeId === a.id);
      const prefB = preferences.find(p => p.employeeId === b.id);
      
      const priorityA = prefA?.priorityLevel || 1;
      const priorityB = prefB?.priorityLevel || 1;
      
      // Higher priority first, then by fairness (could add shift count logic here)
      return priorityB - priorityA;
    });

    // Take only the required number of staff
    return sortedEmployees.slice(0, template.requiredStaffCount);
  }

  /**
   * Validate and optimize schedules based on rules and constraints
   */
  private async validateAndOptimizeSchedules(
    proposals: Partial<Schedule>[],
    employees: Employee[],
    preferences: EmployeeShiftPreference[],
    rules: SchedulingRule[],
    existingSchedules: Schedule[],
    leaveRequests: LeaveRequest[]
  ): Promise<Partial<Schedule>[]> {
    const validSchedules: Partial<Schedule>[] = [];
    const conflicts: string[] = [];

    for (const proposal of proposals) {
      let isValid = true;
      const employee = employees.find(e => e.id === proposal.employeeId);
      const preference = preferences.find(p => p.employeeId === proposal.employeeId);

      // Check for leave conflicts
      const hasLeave = leaveRequests.some(leave => 
        leave.employeeId === proposal.employeeId &&
        leave.startDate <= proposal.shiftDate! &&
        leave.endDate >= proposal.shiftDate!
      );

      if (hasLeave) {
        isValid = false;
        conflicts.push(`Employee ${employee?.firstName} ${employee?.lastName} has approved leave on ${proposal.shiftDate}`);
      }

      // Check for existing schedule conflicts
      const hasExistingSchedule = existingSchedules.some(schedule =>
        schedule.employeeId === proposal.employeeId &&
        schedule.shiftDate === proposal.shiftDate
      );

      if (hasExistingSchedule) {
        isValid = false;
        conflicts.push(`Employee ${employee?.firstName} ${employee?.lastName} already has a schedule on ${proposal.shiftDate}`);
      }

      // Apply scheduling rules
      for (const rule of rules) {
        if (!this.validateAgainstRule(proposal, rule, validSchedules, preference)) {
          isValid = false;
          conflicts.push(`Schedule violates rule: ${rule.ruleName}`);
        }
      }

      if (isValid) {
        validSchedules.push(proposal);
      }
    }

    return validSchedules;
  }

  /**
   * Validate a schedule proposal against a specific rule
   */
  private validateAgainstRule(
    proposal: Partial<Schedule>,
    rule: SchedulingRule,
    existingValidSchedules: Partial<Schedule>[],
    preference?: EmployeeShiftPreference
  ): boolean {
    switch (rule.ruleType) {
      case 'constraint':
        return this.validateConstraintRule(proposal, rule, existingValidSchedules, preference);
      case 'fairness':
        return this.validateFairnessRule(proposal, rule, existingValidSchedules);
      case 'coverage':
        return this.validateCoverageRule(proposal, rule, existingValidSchedules);
      case 'preference':
        return this.validatePreferenceRule(proposal, rule, preference);
      default:
        return true;
    }
  }

  private validateConstraintRule(
    proposal: Partial<Schedule>,
    rule: SchedulingRule,
    existingValidSchedules: Partial<Schedule>[],
    preference?: EmployeeShiftPreference
  ): boolean {
    const config = rule.ruleConfig;

    // Minimum rest period between shifts
    if (config.min_hours_between_shifts) {
      const employeeSchedules = existingValidSchedules.filter(s => s.employeeId === proposal.employeeId);
      // Add logic to check time between shifts
      // This would require more complex date/time calculations
    }

    // Maximum consecutive days
    if (preference?.maxConsecutiveDays) {
      // Add logic to check consecutive days
    }

    return true;
  }

  private validateFairnessRule(
    proposal: Partial<Schedule>,
    rule: SchedulingRule,
    existingValidSchedules: Partial<Schedule>[]
  ): boolean {
    // Add fairness validation logic
    return true;
  }

  private validateCoverageRule(
    proposal: Partial<Schedule>,
    rule: SchedulingRule,
    existingValidSchedules: Partial<Schedule>[]
  ): boolean {
    // Add coverage validation logic
    return true;
  }

  private validatePreferenceRule(
    proposal: Partial<Schedule>,
    rule: SchedulingRule,
    preference?: EmployeeShiftPreference
  ): boolean {
    // Add preference validation logic
    return true;
  }

  /**
   * Save validated schedules to database
   */
  private async saveSchedules(
    schedules: Partial<Schedule>[],
    overrideExisting: boolean = false
  ): Promise<SchedulingResult> {
    const conflicts: string[] = [];
    const warnings: string[] = [];
    let schedulesCreated = 0;

    try {
      if (overrideExisting) {
        // Delete existing schedules in the date range first
        const dateRange = schedules.map(s => s.shiftDate).filter(Boolean);
        if (dateRange.length > 0) {
          const minDate = Math.min(...dateRange.map(d => new Date(d!).getTime()));
          const maxDate = Math.max(...dateRange.map(d => new Date(d!).getTime()));
          
          await supabase
            .from('schedules')
            .delete()
            .gte('shift_date', new Date(minDate).toISOString().split('T')[0])
            .lte('shift_date', new Date(maxDate).toISOString().split('T')[0]);
        }
      }

      // Insert new schedules
      const { data, error } = await supabase
        .from('schedules')
        .insert(schedules)
        .select();

      if (error) {
        conflicts.push(`Database error: ${error.message}`);
        return {
          success: false,
          schedulesCreated: 0,
          conflicts,
          warnings,
          schedules: []
        };
      }

      schedulesCreated = data?.length || 0;

      return {
        success: true,
        schedulesCreated,
        conflicts,
        warnings,
        schedules: data || []
      };
    } catch (error) {
      return {
        success: false,
        schedulesCreated: 0,
        conflicts: [`Error saving schedules: ${error.message}`],
        warnings,
        schedules: []
      };
    }
  }
}

export const automaticSchedulingService = new AutomaticSchedulingService();
