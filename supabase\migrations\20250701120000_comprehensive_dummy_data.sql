-- Comprehensive Dummy Data for Employee Management System
-- Focus on Schedule Management and Leave Management modules

-- First, let's add more employees to have sufficient data for testing
INSERT INTO public.employees (
  employee_id, first_name, last_name, email, phone, role, department, 
  position, join_date, status, shift, salary, certifications, skills,
  address, emergency_contact
) VALUES
  -- Additional Doctors
  ('DOC003', 'Dr. <PERSON><PERSON>', 'Wijaya', '<EMAIL>', '+62 818-9012-3456', 'doctor', 'orthopedics', 'Orthopedic Surgeon', '2019-05-15', 'active', 'morning', 28000000, ARRAY['Orthopedic Surgery', 'Sports Medicine'], ARRAY['Joint Replacement', 'Arthroscopy'], 'Jl. Sudirman No. 123, Jakarta', '{"name": "<PERSON><PERSON> Wija<PERSON>", "phone": "+62 819-1234-5678", "relationship": "<PERSON><PERSON>"}'),
  ('DOC004', 'Dr. <PERSON>na', '<PERSON><PERSON>', '<EMAIL>', '+62 820-2345-6789', 'doctor', 'pediatrics', 'Pediatrician', '2021-02-10', 'active', 'afternoon', 24000000, AR<PERSON><PERSON>['Pediatrics', 'Child Development'], AR<PERSON>Y['Child Care', 'Vaccination'], 'Jl. Thamrin No. 456, Jakarta', '{"name": "Budi <PERSON>o", "phone": "+62 821-2345-6789", "relationship": "Suami"}'),
  ('DOC005', 'Dr. He<PERSON>', 'Kusuma', '<EMAIL>', '+62 822-3456-7890', 'doctor', 'emergency', 'Emergency Physician', '2020-08-20', 'active', 'night', 26000000, ARRAY['Emergency Medicine', 'Trauma Care'], ARRAY['Critical Care', 'Resuscitation'], 'Jl. Gatot Subroto No. 789, Jakarta', '{"name": "Maya Kusuma", "phone": "+62 823-3456-7890", "relationship": "Istri"}'),
  
  -- Additional Nurses
  ('NUR003', 'Dewi', 'Lestari', '<EMAIL>', '+62 824-4567-8901', 'nurse', 'surgery', 'Operating Room Nurse', '2020-03-12', 'active', 'morning', 9000000, ARRAY['OR Nursing', 'Sterile Technique'], ARRAY['Surgical Assistance', 'Equipment Management'], 'Jl. Rasuna Said No. 321, Jakarta', '{"name": "Agus Lestari", "phone": "+62 825-4567-8901", "relationship": "Suami"}'),
  ('NUR004', 'Rina', 'Handayani', '<EMAIL>', '+62 826-5678-9012', 'nurse', 'cardiology', 'Cardiac Nurse', '2019-11-08', 'active', 'afternoon', 8500000, ARRAY['Cardiac Nursing', 'ECG Monitoring'], ARRAY['Cardiac Care', 'Patient Monitoring'], 'Jl. HR Rasuna Said No. 654, Jakarta', '{"name": "Dedi Handayani", "phone": "+62 827-5678-9012", "relationship": "Suami"}'),
  ('NUR005', 'Indah', 'Permata', '<EMAIL>', '+62 828-6789-0123', 'nurse', 'orthopedics', 'Orthopedic Nurse', '2021-07-15', 'active', 'night', 7500000, ARRAY['Orthopedic Nursing', 'Rehabilitation'], ARRAY['Post-op Care', 'Mobility Assistance'], 'Jl. Kuningan No. 987, Jakarta', '{"name": "Rudi Permata", "phone": "+62 829-6789-0123", "relationship": "Suami"}'),
  
  -- Additional Support Staff
  ('TEC002', 'Bambang', 'Sutrisno', '<EMAIL>', '+62 830-7890-1234', 'technician', 'radiology', 'Radiology Technician', '2020-01-20', 'active', 'morning', 6500000, ARRAY['Radiology Technology', 'Radiation Safety'], ARRAY['X-Ray Operation', 'CT Scan'], 'Jl. Senayan No. 147, Jakarta', '{"name": "Sri Sutrisno", "phone": "+62 831-7890-1234", "relationship": "Istri"}'),
  ('PHA002', 'Lina', 'Marlina', '<EMAIL>', '+62 832-8901-2345', 'pharmacist', 'pharmacy', 'Clinical Pharmacist', '2019-09-05', 'active', 'afternoon', 11000000, ARRAY['Clinical Pharmacy', 'Drug Information'], ARRAY['Medication Review', 'Patient Counseling'], 'Jl. Menteng No. 258, Jakarta', '{"name": "Eko Marlina", "phone": "+62 833-8901-2345", "relationship": "Suami"}'),
  ('REC001', 'Sinta', 'Dewi', '<EMAIL>', '+62 834-9012-3456', 'receptionist', 'administration', 'Front Desk Receptionist', '2022-04-10', 'active', 'morning', 5000000, ARRAY['Customer Service', 'Medical Records'], ARRAY['Patient Registration', 'Appointment Scheduling'], 'Jl. Cikini No. 369, Jakarta', '{"name": "Andi Dewi", "phone": "+62 835-9012-3456", "relationship": "Suami"}'),
  ('MAN001', 'Dr. Budi', 'Santoso', '<EMAIL>', '+62 836-0123-4567', 'manager', 'administration', 'Hospital Administrator', '2018-01-15', 'active', 'morning', 35000000, ARRAY['Healthcare Management', 'Hospital Administration'], ARRAY['Strategic Planning', 'Quality Management'], 'Jl. Kemang No. 741, Jakarta', '{"name": "Ratna Santoso", "phone": "+62 837-0123-4567", "relationship": "Istri"}');

-- Generate comprehensive schedule data for the next 3 months
-- This includes various shift patterns, rotations, and realistic scheduling scenarios
WITH 
  all_employees AS (
    SELECT id, first_name, last_name, department, shift, role
    FROM employees 
    WHERE status = 'active'
  ),
  date_range AS (
    SELECT generate_series(
      CURRENT_DATE - INTERVAL '30 days',
      CURRENT_DATE + INTERVAL '90 days',
      INTERVAL '1 day'
    )::date as schedule_date
  ),
  shift_patterns AS (
    SELECT 
      e.id as employee_id,
      e.first_name,
      e.last_name,
      e.department,
      e.shift as preferred_shift,
      e.role,
      d.schedule_date,
      -- Create realistic shift patterns based on employee's preferred shift and role
      CASE 
        WHEN e.shift = 'morning' THEN 
          CASE 
            WHEN EXTRACT(dow FROM d.schedule_date) IN (0, 6) THEN NULL -- Weekend off for some
            ELSE 'morning'
          END
        WHEN e.shift = 'afternoon' THEN
          CASE 
            WHEN EXTRACT(dow FROM d.schedule_date) IN (0, 6) THEN NULL
            ELSE 'afternoon'
          END
        WHEN e.shift = 'night' THEN
          CASE 
            WHEN EXTRACT(dow FROM d.schedule_date) IN (1, 3, 5) THEN 'night' -- 3 nights per week
            ELSE NULL
          END
        WHEN e.shift = 'rotating' THEN
          CASE 
            WHEN EXTRACT(dow FROM d.schedule_date) % 3 = 0 THEN 'morning'
            WHEN EXTRACT(dow FROM d.schedule_date) % 3 = 1 THEN 'afternoon'
            ELSE 'night'
          END
      END as shift_type,
      -- Emergency department needs 24/7 coverage
      CASE 
        WHEN e.department = 'emergency' THEN
          CASE 
            WHEN EXTRACT(dow FROM d.schedule_date) % 3 = 0 THEN 'morning'
            WHEN EXTRACT(dow FROM d.schedule_date) % 3 = 1 THEN 'afternoon'
            ELSE 'night'
          END
        ELSE NULL
      END as emergency_override
    FROM all_employees e
    CROSS JOIN date_range d
  )
INSERT INTO public.schedules (
  employee_id, shift_date, shift_type, start_time, end_time, status, notes
)
SELECT 
  sp.employee_id,
  sp.schedule_date,
  COALESCE(sp.emergency_override, sp.shift_type)::shift_type,
  CASE 
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'morning' THEN '07:00'::time
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'afternoon' THEN '15:00'::time
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'night' THEN '23:00'::time
  END,
  CASE 
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'morning' THEN '15:00'::time
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'afternoon' THEN '23:00'::time
    WHEN COALESCE(sp.emergency_override, sp.shift_type) = 'night' THEN '07:00'::time
  END,
  CASE 
    WHEN sp.schedule_date < CURRENT_DATE THEN 
      CASE 
        WHEN random() < 0.85 THEN 'completed'::shift_status
        WHEN random() < 0.95 THEN 'scheduled'::shift_status
        ELSE 'no_show'::shift_status
      END
    WHEN sp.schedule_date = CURRENT_DATE THEN 'scheduled'::shift_status
    ELSE 'scheduled'::shift_status
  END,
  CASE 
    WHEN sp.department = 'emergency' THEN 'Emergency department coverage'
    WHEN sp.preferred_shift = 'rotating' THEN 'Rotating shift assignment'
    WHEN EXTRACT(dow FROM sp.schedule_date) IN (0, 6) THEN 'Weekend shift'
    ELSE 'Regular shift assignment'
  END
FROM shift_patterns sp
WHERE COALESCE(sp.emergency_override, sp.shift_type) IS NOT NULL
ON CONFLICT (employee_id, shift_date, start_time) DO NOTHING;

-- Create additional tables for enhanced leave management
-- Leave balance tracking table
CREATE TABLE IF NOT EXISTS public.leave_balances (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  year INTEGER NOT NULL,
  annual_leave_total INTEGER NOT NULL DEFAULT 12, -- 12 days per year standard in Indonesia
  annual_leave_used INTEGER NOT NULL DEFAULT 0,
  annual_leave_remaining INTEGER GENERATED ALWAYS AS (annual_leave_total - annual_leave_used) STORED,
  sick_leave_used INTEGER NOT NULL DEFAULT 0,
  emergency_leave_used INTEGER NOT NULL DEFAULT 0,
  maternity_leave_used INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(employee_id, year)
);

-- Leave approval workflow table
CREATE TABLE IF NOT EXISTS public.leave_approvals (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  leave_request_id UUID NOT NULL REFERENCES leave_requests(id) ON DELETE CASCADE,
  approver_id UUID NOT NULL REFERENCES employees(id),
  approval_level INTEGER NOT NULL, -- 1: Direct supervisor, 2: Department head, 3: HR
  status TEXT NOT NULL DEFAULT 'pending', -- pending, approved, rejected
  comments TEXT,
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Medical certificates table for sick leave
CREATE TABLE IF NOT EXISTS public.medical_certificates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  leave_request_id UUID NOT NULL REFERENCES leave_requests(id) ON DELETE CASCADE,
  doctor_name TEXT NOT NULL,
  hospital_clinic TEXT NOT NULL,
  diagnosis TEXT,
  certificate_number TEXT,
  issue_date DATE NOT NULL,
  file_path TEXT, -- Reference to uploaded file
  verified BOOLEAN DEFAULT FALSE,
  verified_by UUID REFERENCES employees(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Schedule replacement/substitution table
CREATE TABLE IF NOT EXISTS public.schedule_replacements (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  original_schedule_id UUID NOT NULL REFERENCES schedules(id) ON DELETE CASCADE,
  replacement_employee_id UUID NOT NULL REFERENCES employees(id),
  reason TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, approved, rejected, completed
  requested_by UUID NOT NULL REFERENCES employees(id),
  approved_by UUID REFERENCES employees(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table for schedule and leave changes
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  recipient_id UUID NOT NULL REFERENCES employees(id),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL, -- schedule_change, leave_approved, leave_rejected, replacement_request, etc.
  related_id UUID, -- ID of related schedule, leave request, etc.
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for new tables
ALTER TABLE public.leave_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leave_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medical_certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.schedule_replacements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for new tables
CREATE POLICY "Authenticated users can view leave balances" ON public.leave_balances FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can update leave balances" ON public.leave_balances FOR ALL TO authenticated USING (true);

CREATE POLICY "Authenticated users can view leave approvals" ON public.leave_approvals FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can manage leave approvals" ON public.leave_approvals FOR ALL TO authenticated USING (true);

CREATE POLICY "Authenticated users can view medical certificates" ON public.medical_certificates FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can manage medical certificates" ON public.medical_certificates FOR ALL TO authenticated USING (true);

CREATE POLICY "Authenticated users can view schedule replacements" ON public.schedule_replacements FOR SELECT TO authenticated USING (true);
CREATE POLICY "Authenticated users can manage schedule replacements" ON public.schedule_replacements FOR ALL TO authenticated USING (true);

CREATE POLICY "Users can view their notifications" ON public.notifications FOR SELECT TO authenticated USING (recipient_id IN (SELECT id FROM employees WHERE email = auth.email()));
CREATE POLICY "Authenticated users can manage notifications" ON public.notifications FOR ALL TO authenticated USING (true);

-- Insert leave balance data for all active employees
INSERT INTO public.leave_balances (employee_id, year, annual_leave_total, annual_leave_used, sick_leave_used, emergency_leave_used, maternity_leave_used)
SELECT
  e.id,
  EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER,
  CASE
    WHEN EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.join_date::date) >= 5 THEN 15 -- Senior employees get more leave
    ELSE 12
  END,
  FLOOR(random() * 8)::INTEGER, -- Used 0-7 days of annual leave
  FLOOR(random() * 5)::INTEGER, -- Used 0-4 days of sick leave
  FLOOR(random() * 3)::INTEGER, -- Used 0-2 days of emergency leave
  CASE
    WHEN e.role = 'nurse' AND random() < 0.3 THEN FLOOR(random() * 90)::INTEGER -- Some nurses took maternity leave
    ELSE 0
  END
FROM employees e
WHERE e.status = 'active';

-- Insert previous year's leave balance data
INSERT INTO public.leave_balances (employee_id, year, annual_leave_total, annual_leave_used, sick_leave_used, emergency_leave_used, maternity_leave_used)
SELECT
  e.id,
  EXTRACT(YEAR FROM CURRENT_DATE)::INTEGER - 1,
  12,
  FLOOR(random() * 12 + 3)::INTEGER, -- Used 3-14 days (more usage in previous year)
  FLOOR(random() * 8)::INTEGER, -- Used 0-7 days of sick leave
  FLOOR(random() * 4)::INTEGER, -- Used 0-3 days of emergency leave
  CASE
    WHEN e.role = 'nurse' AND random() < 0.2 THEN 90 -- Some nurses took full maternity leave
    ELSE 0
  END
FROM employees e
WHERE e.status = 'active' AND e.join_date::date < (CURRENT_DATE - INTERVAL '1 year');

-- Generate comprehensive leave request data with various scenarios
-- WITH
--   leave_scenarios AS (
--     SELECT
--       e.id as employee_id,
--       e.first_name,
--       e.last_name,
--       e.department,
--       e.role,
--       -- Generate multiple leave requests per employee
--       generate_series(1, CASE WHEN random() < 0.7 THEN 2 ELSE 3 END) as request_num
--     FROM employees e
--     WHERE e.status = 'active'
--   ),
--   leave_types_data AS (
--     SELECT * FROM (VALUES
--       ('Cuti Tahunan', 'annual', 1, 14),
--       ('Cuti Sakit', 'sick', 1, 7),
--       ('Cuti Melahirkan', 'maternity', 90, 90),
--       ('Cuti Menikah', 'marriage', 3, 3),
--       ('Cuti Duka Cita', 'bereavement', 2, 3),
--       ('Cuti Haji/Umroh', 'pilgrimage', 14, 40),
--       ('Cuti Darurat Keluarga', 'emergency', 1, 2),
--       ('Cuti Pendidikan', 'education', 5, 30)
--     ) AS t(name, category, min_days, max_days)
--   )
-- INSERT INTO public.leave_requests (
--   employee_id, leave_type, start_date, end_date, reason, status, approved_by, created_at, updated_at
-- )
-- SELECT
--   ls.employee_id,
--   ltd.name,
--   -- Generate realistic date ranges
--   CASE
--     WHEN ls.request_num = 1 THEN
--       CURRENT_DATE - INTERVAL '45 days' + (random() * 30)::INTEGER * INTERVAL '1 day'
--     WHEN ls.request_num = 2 THEN
--       CURRENT_DATE - INTERVAL '15 days' + (random() * 20)::INTEGER * INTERVAL '1 day'
--     ELSE
--       CURRENT_DATE + (random() * 60)::INTEGER * INTERVAL '1 day'
--   END as start_date,
--   CASE
--     WHEN ls.request_num = 1 THEN
--       CURRENT_DATE - INTERVAL '45 days' + (random() * 30)::INTEGER * INTERVAL '1 day' +
--       (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
--     WHEN ls.request_num = 2 THEN
--       CURRENT_DATE - INTERVAL '15 days' + (random() * 20)::INTEGER * INTERVAL '1 day' +
--       (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
--     ELSE
--       CURRENT_DATE + (random() * 60)::INTEGER * INTERVAL '1 day' +
--       (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
--   END as end_date,
--   -- Generate realistic reasons based on leave type
--   CASE
--     WHEN ltd.category = 'annual' THEN
--       CASE
--         WHEN random() < 0.3 THEN 'Liburan keluarga'
--         WHEN random() < 0.6 THEN 'Keperluan pribadi'
--         ELSE 'Istirahat dan refreshing'
--       END
--     WHEN ltd.category = 'sick' THEN
--       CASE
--         WHEN random() < 0.4 THEN 'Demam dan flu'
--         WHEN random() < 0.7 THEN 'Sakit perut dan diare'
--         ELSE 'Sakit kepala dan pusing'
--       END
--     WHEN ltd.category = 'maternity' THEN 'Cuti melahirkan sesuai peraturan'
--     WHEN ltd.category = 'marriage' THEN 'Menikah dengan pasangan'
--     WHEN ltd.category = 'bereavement' THEN 'Meninggalnya anggota keluarga'
--     WHEN ltd.category = 'pilgrimage' THEN 'Menunaikan ibadah haji/umroh'
--     WHEN ltd.category = 'emergency' THEN 'Keadaan darurat keluarga'
--     WHEN ltd.category = 'education' THEN 'Mengikuti pelatihan/seminar'
--   END,
--   -- Generate realistic status distribution
--   CASE
--     WHEN ls.request_num = 1 THEN
--       CASE
--         WHEN random() < 0.7 THEN 'approved'
--         WHEN random() < 0.9 THEN 'rejected'
--         ELSE 'pending'
--       END
--     WHEN ls.request_num = 2 THEN
--       CASE
--         WHEN random() < 0.8 THEN 'approved'
--         WHEN random() < 0.95 THEN 'pending'
--         ELSE 'rejected'
--       END
--     ELSE 'pending' -- Future requests are pending
--   END,
--   -- Assign approver for approved/rejected requests
--   CASE
--     WHEN ls.request_num <= 2 AND random() < 0.8 THEN
--       (SELECT id FROM employees WHERE role = 'manager' AND department = 'administration' LIMIT 1)
--     ELSE NULL
--   END,
--   -- Created timestamp
--   CASE
--     WHEN ls.request_num = 1 THEN
--       CURRENT_TIMESTAMP - INTERVAL '50 days' + (random() * 35)::INTEGER * INTERVAL '1 day'
--     WHEN ls.request_num = 2 THEN
--       CURRENT_TIMESTAMP - INTERVAL '20 days' + (random() * 15)::INTEGER * INTERVAL '1 day'
--     ELSE
--       CURRENT_TIMESTAMP - (random() * 5)::INTEGER * INTERVAL '1 day'
--   END,
--   CURRENT_TIMESTAMP
-- FROM leave_scenarios ls
-- CROSS JOIN leave_types_data ltd
-- WHERE
--   -- Realistic distribution of leave types
--   (ltd.category = 'annual' AND random() < 0.6) OR
--   (ltd.category = 'sick' AND random() < 0.4) OR
--   (ltd.category = 'maternity' AND ls.role = 'nurse' AND random() < 0.1) OR
--   (ltd.category = 'marriage' AND random() < 0.05) OR
--   (ltd.category = 'bereavement' AND random() < 0.03) OR
--   (ltd.category = 'pilgrimage' AND random() < 0.02) OR
--   (ltd.category = 'emergency' AND random() < 0.08) OR
--   (ltd.category = 'education' AND ls.role IN ('doctor', 'nurse', 'pharmacist') AND random() < 0.1);

-- BARU
WITH
  leave_scenarios AS (
    SELECT
      e.id as employee_id,
      e.first_name,
      e.last_name,
      e.department,
      e.role,
      -- Generate multiple leave requests per employee
      generate_series(1, CASE WHEN random() < 0.7 THEN 2 ELSE 3 END) as request_num
    FROM employees e
    WHERE e.status = 'active'
  ),
  leave_types_data AS (
    SELECT * FROM (VALUES
      ('Cuti Tahunan', 'annual', 1, 14),
      ('Cuti Sakit', 'sick', 1, 7),
      ('Cuti Melahirkan', 'maternity', 90, 90),
      ('Cuti Menikah', 'marriage', 3, 3),
      ('Cuti Duka Cita', 'bereavement', 2, 3),
      ('Cuti Haji/Umroh', 'pilgrimage', 14, 40),
      ('Cuti Darurat Keluarga', 'emergency', 1, 2),
      ('Cuti Pendidikan', 'education', 5, 30)
    ) AS t(name, category, min_days, max_days)
  )
INSERT INTO public.leave_requests (
  employee_id, leave_type, start_date, end_date, reason, status, created_at, updated_at
)
SELECT
  ls.employee_id,
  ltd.name,
  -- Generate realistic date ranges
  CASE
    WHEN ls.request_num = 1 THEN
      CURRENT_DATE - INTERVAL '45 days' + (random() * 30)::INTEGER * INTERVAL '1 day'
    WHEN ls.request_num = 2 THEN
      CURRENT_DATE - INTERVAL '15 days' + (random() * 20)::INTEGER * INTERVAL '1 day'
    ELSE
      CURRENT_DATE + (random() * 60)::INTEGER * INTERVAL '1 day'
  END as start_date,
  CASE
    WHEN ls.request_num = 1 THEN
      CURRENT_DATE - INTERVAL '45 days' + (random() * 30)::INTEGER * INTERVAL '1 day' +
      (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
    WHEN ls.request_num = 2 THEN
      CURRENT_DATE - INTERVAL '15 days' + (random() * 20)::INTEGER * INTERVAL '1 day' +
      (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
    ELSE
      CURRENT_DATE + (random() * 60)::INTEGER * INTERVAL '1 day' +
      (ltd.min_days + random() * (ltd.max_days - ltd.min_days))::INTEGER * INTERVAL '1 day'
  END as end_date,
  -- Generate realistic reasons based on leave type
  CASE
    WHEN ltd.category = 'annual' THEN
      CASE
        WHEN random() < 0.3 THEN 'Liburan keluarga'
        WHEN random() < 0.6 THEN 'Keperluan pribadi'
        ELSE 'Istirahat dan refreshing'
      END
    WHEN ltd.category = 'sick' THEN
      CASE
        WHEN random() < 0.4 THEN 'Demam dan flu'
        WHEN random() < 0.7 THEN 'Sakit perut dan diare'
        ELSE 'Sakit kepala dan pusing'
      END
    WHEN ltd.category = 'maternity' THEN 'Cuti melahirkan sesuai peraturan'
    WHEN ltd.category = 'marriage' THEN 'Menikah dengan pasangan'
    WHEN ltd.category = 'bereavement' THEN 'Meninggalnya anggota keluarga'
    WHEN ltd.category = 'pilgrimage' THEN 'Menunaikan ibadah haji/umroh'
    WHEN ltd.category = 'emergency' THEN 'Keadaan darurat keluarga'
    WHEN ltd.category = 'education' THEN 'Mengikuti pelatihan/seminar'
  END,
  -- Generate realistic status distribution
  CASE
    WHEN ls.request_num = 1 THEN
      CASE
        WHEN random() < 0.7 THEN 'approved'
        WHEN random() < 0.9 THEN 'rejected'
        ELSE 'pending'
      END
    WHEN ls.request_num = 2 THEN
      CASE
        WHEN random() < 0.8 THEN 'approved'
        WHEN random() < 0.95 THEN 'pending'
        ELSE 'rejected'
      END
    ELSE 'pending' -- Future requests are pending
  END,
  -- Created timestamp
  CASE
    WHEN ls.request_num = 1 THEN
      CURRENT_TIMESTAMP - INTERVAL '50 days' + (random() * 35)::INTEGER * INTERVAL '1 day'
    WHEN ls.request_num = 2 THEN
      CURRENT_TIMESTAMP - INTERVAL '20 days' + (random() * 15)::INTEGER * INTERVAL '1 day'
    ELSE
      CURRENT_TIMESTAMP - (random() * 5)::INTEGER * INTERVAL '1 day'
  END,
  CURRENT_TIMESTAMP
FROM leave_scenarios ls
CROSS JOIN leave_types_data ltd
WHERE
  -- Realistic distribution of leave types
  (ltd.category = 'annual' AND random() < 0.6) OR
  (ltd.category = 'sick' AND random() < 0.4) OR
  (ltd.category = 'maternity' AND ls.role = 'nurse' AND random() < 0.1) OR
  (ltd.category = 'marriage' AND random() < 0.05) OR
  (ltd.category = 'bereavement' AND random() < 0.03) OR
  (ltd.category = 'pilgrimage' AND random() < 0.02) OR
  (ltd.category = 'emergency' AND random() < 0.08) OR
  (ltd.category = 'education' AND ls.role IN ('doctor', 'nurse', 'pharmacist') AND random() < 0.1);

-- Update approved_by with correct auth.users IDs
-- The approved_by column references auth.users table, so we need to find corresponding user IDs

-- Simple approach: All approvals by administration manager
UPDATE leave_requests 
SET approved_by = (
  SELECT au.id 
  FROM auth.users au
  JOIN employees e ON au.email = e.email
  WHERE e.role = 'manager' 
  AND e.department = 'administration' 
  LIMIT 1
)
WHERE status IN ('approved', 'rejected') AND approved_by IS NULL;

-- Alternative: Department-specific managers (if you want different managers per department)
UPDATE leave_requests lr
SET approved_by = (
  SELECT au.id 
  FROM auth.users au
  JOIN employees mgr ON au.email = mgr.email
  JOIN employees emp ON emp.id = lr.employee_id
  WHERE mgr.role = 'manager' 
  AND (mgr.department = emp.department OR mgr.department = 'administration')
  ORDER BY CASE WHEN mgr.department = emp.department THEN 1 ELSE 2 END
  LIMIT 1
)
WHERE lr.status IN ('approved', 'rejected') AND lr.approved_by IS NULL;

-- / BARU

-- Insert leave approval workflow data
INSERT INTO public.leave_approvals (leave_request_id, approver_id, approval_level, status, comments, approved_at)
SELECT
  lr.id,
  CASE
    WHEN random() < 0.6 THEN
      (SELECT id FROM employees WHERE role = 'manager' AND department = 'administration' LIMIT 1)
    ELSE
      (SELECT id FROM employees WHERE role = 'manager' AND department = 'administration' LIMIT 1)
  END,
  CASE
    WHEN lr.leave_type IN ('Cuti Melahirkan', 'Cuti Haji/Umroh') THEN 2 -- Higher approval needed
    ELSE 1
  END,
  lr.status,
  CASE
    WHEN lr.status = 'approved' THEN
      CASE
        WHEN random() < 0.3 THEN 'Disetujui sesuai peraturan perusahaan'
        WHEN random() < 0.6 THEN 'Cuti disetujui, harap koordinasi dengan tim'
        ELSE 'Approved'
      END
    WHEN lr.status = 'rejected' THEN
      CASE
        WHEN random() < 0.5 THEN 'Tidak dapat disetujui karena jadwal operasional'
        ELSE 'Ditolak, silakan ajukan di waktu yang berbeda'
      END
    ELSE NULL
  END,
  CASE
    WHEN lr.status IN ('approved', 'rejected') THEN lr.updated_at
    ELSE NULL
  END
FROM leave_requests lr
JOIN employees e ON lr.employee_id = e.id
WHERE lr.status != 'pending';

-- Insert medical certificates for sick leave requests
INSERT INTO public.medical_certificates (
  leave_request_id, doctor_name, hospital_clinic, diagnosis, certificate_number,
  issue_date, file_path, verified, verified_by, verified_at
)
SELECT
  lr.id,
  CASE
    WHEN random() < 0.25 THEN 'Dr. Ahmad Fauzi, Sp.PD'
    WHEN random() < 0.5 THEN 'Dr. Siti Rahayu, Sp.A'
    WHEN random() < 0.75 THEN 'Dr. Budi Hartono, Sp.OG'
    ELSE 'Dr. Maya Sari, Sp.JP'
  END,
  CASE
    WHEN random() < 0.3 THEN 'RS Siloam Hospitals'
    WHEN random() < 0.6 THEN 'RS Pondok Indah'
    ELSE 'Klinik Kimia Farma'
  END,
  CASE
    WHEN random() < 0.3 THEN 'Influenza dan demam tinggi'
    WHEN random() < 0.6 THEN 'Gastroenteritis akut'
    ELSE 'Migrain dan vertigo'
  END,
  'SKD-' || LPAD((random() * 999999)::INTEGER::TEXT, 6, '0'),
  lr.start_date,
  '/uploads/medical_certificates/' || lr.id || '_certificate.pdf',
  CASE WHEN random() < 0.8 THEN TRUE ELSE FALSE END,
  CASE
    WHEN random() < 0.8 THEN
      (SELECT id FROM employees WHERE role = 'manager' AND department = 'administration' LIMIT 1)
    ELSE NULL
  END,
  CASE
    WHEN random() < 0.8 THEN lr.updated_at + INTERVAL '1 day'
    ELSE NULL
  END
FROM leave_requests lr
WHERE lr.leave_type = 'Cuti Sakit' AND lr.status = 'approved';

-- Generate schedule replacement requests
WITH replacement_scenarios AS (
  SELECT
    s.id as original_schedule_id,
    s.employee_id as original_employee_id,
    s.shift_date,
    s.shift_type,
    e.department,
    -- Find potential replacement employees from same department
    (SELECT id FROM employees e2
     WHERE e2.department = e.department
     AND e2.id != s.employee_id
     AND e2.status = 'active'
     AND e2.shift IN (s.shift_type, 'rotating')
     ORDER BY random()
     LIMIT 1) as replacement_employee_id
  FROM schedules s
  JOIN employees e ON s.employee_id = e.id
  WHERE s.shift_date BETWEEN CURRENT_DATE - INTERVAL '30 days' AND CURRENT_DATE + INTERVAL '30 days'
  AND random() < 0.05 -- 5% of schedules need replacement
)
INSERT INTO public.schedule_replacements (
  original_schedule_id, replacement_employee_id, reason, status,
  requested_by, approved_by, notes
)
SELECT
  rs.original_schedule_id,
  rs.replacement_employee_id,
  CASE
    WHEN random() < 0.4 THEN 'Karyawan sakit mendadak'
    WHEN random() < 0.7 THEN 'Keperluan keluarga mendesak'
    ELSE 'Konflik jadwal pribadi'
  END,
  CASE
    WHEN rs.shift_date < CURRENT_DATE THEN
      CASE WHEN random() < 0.8 THEN 'completed' ELSE 'approved' END
    ELSE
      CASE WHEN random() < 0.6 THEN 'approved' ELSE 'pending' END
  END,
  rs.original_employee_id,
  CASE
    WHEN random() < 0.7 THEN
      (SELECT id FROM employees WHERE role = 'manager' AND department = rs.department LIMIT 1)
    ELSE NULL
  END,
  'Penggantian shift sesuai prosedur'
FROM replacement_scenarios rs
WHERE rs.replacement_employee_id IS NOT NULL;

-- Generate comprehensive notification data
-- Notifications for leave request approvals/rejections
INSERT INTO public.notifications (recipient_id, title, message, type, related_id, read)
SELECT
  lr.employee_id,
  CASE
    WHEN lr.status = 'approved' THEN 'Permohonan Cuti Disetujui'
    WHEN lr.status = 'rejected' THEN 'Permohonan Cuti Ditolak'
    ELSE 'Permohonan Cuti Sedang Diproses'
  END,
  CASE
    WHEN lr.status = 'approved' THEN
      'Permohonan cuti ' || lr.leave_type || ' Anda dari tanggal ' ||
      TO_CHAR(lr.start_date::date, 'DD/MM/YYYY') || ' sampai ' ||
      TO_CHAR(lr.end_date::date, 'DD/MM/YYYY') || ' telah disetujui.'
    WHEN lr.status = 'rejected' THEN
      'Permohonan cuti ' || lr.leave_type || ' Anda dari tanggal ' ||
      TO_CHAR(lr.start_date::date, 'DD/MM/YYYY') || ' sampai ' ||
      TO_CHAR(lr.end_date::date, 'DD/MM/YYYY') || ' ditolak. Silakan hubungi atasan untuk informasi lebih lanjut.'
    ELSE
      'Permohonan cuti ' || lr.leave_type || ' Anda sedang dalam proses persetujuan.'
  END,
  CASE
    WHEN lr.status = 'approved' THEN 'leave_approved'
    WHEN lr.status = 'rejected' THEN 'leave_rejected'
    ELSE 'leave_pending'
  END,
  lr.id,
  CASE WHEN random() < 0.6 THEN TRUE ELSE FALSE END
FROM leave_requests lr
WHERE lr.status != 'pending';

-- Notifications for schedule changes and replacements
INSERT INTO public.notifications (recipient_id, title, message, type, related_id, read)
SELECT
  sr.replacement_employee_id,
  'Permintaan Penggantian Shift',
  'Anda diminta untuk menggantikan shift pada tanggal ' ||
  TO_CHAR(s.shift_date, 'DD/MM/YYYY') || ' shift ' || s.shift_type ||
  '. Alasan: ' || sr.reason,
  'replacement_request',
  sr.id,
  CASE WHEN random() < 0.4 THEN TRUE ELSE FALSE END
FROM schedule_replacements sr
JOIN schedules s ON sr.original_schedule_id = s.id
WHERE sr.status IN ('pending', 'approved');

-- Notifications for original employees about replacement status
INSERT INTO public.notifications (recipient_id, title, message, type, related_id, read)
SELECT
  s.employee_id,
  CASE
    WHEN sr.status = 'approved' THEN 'Penggantian Shift Disetujui'
    WHEN sr.status = 'completed' THEN 'Penggantian Shift Selesai'
    ELSE 'Permintaan Penggantian Shift'
  END,
  CASE
    WHEN sr.status = 'approved' THEN
      'Permintaan penggantian shift Anda pada tanggal ' ||
      TO_CHAR(s.shift_date, 'DD/MM/YYYY') || ' telah disetujui.'
    WHEN sr.status = 'completed' THEN
      'Penggantian shift Anda pada tanggal ' ||
      TO_CHAR(s.shift_date, 'DD/MM/YYYY') || ' telah selesai dilaksanakan.'
    ELSE
      'Permintaan penggantian shift Anda sedang diproses.'
  END,
  'replacement_status',
  sr.id,
  CASE WHEN random() < 0.7 THEN TRUE ELSE FALSE END
FROM schedule_replacements sr
JOIN schedules s ON sr.original_schedule_id = s.id;

-- General schedule reminder notifications
INSERT INTO public.notifications (recipient_id, title, message, type, related_id, read)
SELECT
  s.employee_id,
  'Pengingat Jadwal Kerja',
  'Anda memiliki jadwal kerja besok (' || TO_CHAR(s.shift_date, 'DD/MM/YYYY') ||
  ') shift ' || s.shift_type || ' dari ' || s.start_time || ' sampai ' || s.end_time || '.',
  'schedule_reminder',
  s.id,
  CASE WHEN random() < 0.3 THEN TRUE ELSE FALSE END
FROM schedules s
WHERE s.shift_date = CURRENT_DATE + INTERVAL '1 day'
AND s.status = 'scheduled'
AND random() < 0.8; -- Not all employees get reminders

-- Update leave balances based on approved leave requests
UPDATE public.leave_balances lb
SET
  annual_leave_used = lb.annual_leave_used + COALESCE(leave_days.annual_days, 0),
  sick_leave_used = lb.sick_leave_used + COALESCE(leave_days.sick_days, 0),
  emergency_leave_used = lb.emergency_leave_used + COALESCE(leave_days.emergency_days, 0),
  maternity_leave_used = lb.maternity_leave_used + COALESCE(leave_days.maternity_days, 0),
  updated_at = CURRENT_TIMESTAMP
FROM (
  SELECT
    lr.employee_id,
    SUM(CASE WHEN lr.leave_type = 'Cuti Tahunan' THEN (lr.end_date::date - lr.start_date::date + 1) ELSE 0 END) as annual_days,
    SUM(CASE WHEN lr.leave_type = 'Cuti Sakit' THEN (lr.end_date::date - lr.start_date::date + 1) ELSE 0 END) as sick_days,
    SUM(CASE WHEN lr.leave_type = 'Cuti Darurat Keluarga' THEN (lr.end_date::date - lr.start_date::date + 1) ELSE 0 END) as emergency_days,
    SUM(CASE WHEN lr.leave_type = 'Cuti Melahirkan' THEN (lr.end_date::date - lr.start_date::date + 1) ELSE 0 END) as maternity_days
  FROM leave_requests lr
  WHERE lr.status = 'approved'
  AND EXTRACT(YEAR FROM lr.start_date) = EXTRACT(YEAR FROM CURRENT_DATE)
  GROUP BY lr.employee_id
) leave_days
WHERE lb.employee_id = leave_days.employee_id
AND lb.year = EXTRACT(YEAR FROM CURRENT_DATE);

-- Add some additional schedule variations for better testing
-- Weekend emergency coverage
INSERT INTO public.schedules (employee_id, shift_date, shift_type, start_time, end_time, status, notes)
SELECT
  e.id,
  weekend_dates.date_val,
  CASE
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 8 THEN 'morning'::shift_type
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 16 THEN 'afternoon'::shift_type
    ELSE 'night'::shift_type
  END,
  CASE
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 8 THEN '07:00'::time
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 16 THEN '15:00'::time
    ELSE '23:00'::time
  END,
  CASE
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 8 THEN '15:00'::time
    WHEN EXTRACT(hour FROM CURRENT_TIME) < 16 THEN '23:00'::time
    ELSE '07:00'::time
  END,
  'scheduled'::shift_status,
  'Weekend emergency coverage'
FROM employees e
CROSS JOIN (
  SELECT generate_series(
    CURRENT_DATE + INTERVAL '1 day',
    CURRENT_DATE + INTERVAL '30 days',
    INTERVAL '1 day'
  )::date as date_val
) weekend_dates
WHERE e.department = 'emergency'
AND e.status = 'active'
AND EXTRACT(dow FROM weekend_dates.date_val) IN (0, 6) -- Weekends only
AND random() < 0.6 -- 60% coverage
ON CONFLICT (employee_id, shift_date, start_time) DO NOTHING;

-- Final data summary and indexes for better performance
-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_schedules_employee_date ON public.schedules(employee_id, shift_date);
CREATE INDEX IF NOT EXISTS idx_schedules_date_type ON public.schedules(shift_date, shift_type);
CREATE INDEX IF NOT EXISTS idx_schedules_status ON public.schedules(status);

CREATE INDEX IF NOT EXISTS idx_leave_requests_employee ON public.leave_requests(employee_id);
CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON public.leave_requests(status);
CREATE INDEX IF NOT EXISTS idx_leave_requests_dates ON public.leave_requests(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_leave_requests_type ON public.leave_requests(leave_type);

CREATE INDEX IF NOT EXISTS idx_leave_balances_employee_year ON public.leave_balances(employee_id, year);
CREATE INDEX IF NOT EXISTS idx_leave_approvals_request ON public.leave_approvals(leave_request_id);
CREATE INDEX IF NOT EXISTS idx_medical_certificates_request ON public.medical_certificates(leave_request_id);
CREATE INDEX IF NOT EXISTS idx_schedule_replacements_original ON public.schedule_replacements(original_schedule_id);
CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON public.notifications(recipient_id, read);

-- Insert summary comment
INSERT INTO public.notifications (recipient_id, title, message, type, read)
SELECT
  (SELECT id FROM employees WHERE role = 'manager' AND department = 'administration' LIMIT 1),
  'Sistem Data Dummy Berhasil Dimuat',
  'Data dummy komprehensif untuk manajemen jadwal dan cuti telah berhasil dimuat ke dalam sistem. ' ||
  'Data mencakup: ' ||
  (SELECT COUNT(*) FROM schedules)::TEXT || ' jadwal kerja, ' ||
  (SELECT COUNT(*) FROM leave_requests)::TEXT || ' permohonan cuti, ' ||
  (SELECT COUNT(*) FROM leave_balances)::TEXT || ' saldo cuti karyawan, ' ||
  (SELECT COUNT(*) FROM medical_certificates)::TEXT || ' surat keterangan dokter, ' ||
  (SELECT COUNT(*) FROM schedule_replacements)::TEXT || ' permintaan penggantian shift, dan ' ||
  (SELECT COUNT(*) FROM notifications WHERE type != 'system_info')::TEXT || ' notifikasi.',
  'system_info',
  FALSE;
