
import { EmployeeStats } from '@/components/Dashboard/EmployeeStats';
import { RecentActivity } from '@/components/Dashboard/RecentActivity';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Plus, Users, Calendar, FileText, Settings } from 'lucide-react';

export default function Dashboard() {
  const navigate = useNavigate();

  const quickActions = [
    {
      title: 'Tambah Karyawan',
      description: 'Daftarkan karyawan baru',
      icon: Plus,
      action: () => navigate('/employees/add'),
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      title: 'Lihat <PERSON>mu<PERSON>',
      description: 'Kelola data karyawan',
      icon: Users,
      action: () => navigate('/employees'),
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      title: '<PERSON><PERSON><PERSON> Kerja',
      description: 'Atur jadwal shift',
      icon: Calendar,
      action: () => navigate('/schedule'),
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      title: '<PERSON><PERSON><PERSON>',
      description: 'Lihat laporan sistem',
      icon: FileText,
      action: () => navigate('/reports'),
      color: 'bg-orange-500 hover:bg-orange-600',
    },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Selamat datang di Sistem Manajemen Karyawan Rumah Sakit</p>
        </div>
        <Button onClick={() => navigate('/settings')} variant="outline">
          <Settings className="w-4 h-4 mr-2" />
          Pengaturan
        </Button>
      </div>

      {/* Employee Statistics */}
      <EmployeeStats />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Aksi Cepat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Button
                key={index}
                onClick={action.action}
                className={`h-24 flex flex-col items-center justify-center space-y-2 text-white ${action.color}`}
              >
                <action.icon className="h-6 w-6" />
                <div className="text-center">
                  <p className="font-medium">{action.title}</p>
                  <p className="text-xs opacity-90">{action.description}</p>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <RecentActivity />
    </div>
  );
}
