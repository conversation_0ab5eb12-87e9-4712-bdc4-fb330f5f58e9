
import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Calendar, Check, X, Clock, Eye, FileText, CheckCircle, XCircle, AlertTriangle, User } from 'lucide-react';
import { LeaveRequest } from '@/types/schedule';
import { useSchedules } from '@/hooks/useSchedules';
import { LeaveApprovalWorkflow } from './LeaveApprovalWorkflow';
import { useAuth } from '@/hooks/useAuth';

interface LeaveRequestsListProps {
  leaveRequests: LeaveRequest[];
}

export function LeaveRequestsList({ leaveRequests }: LeaveRequestsListProps) {
  const { updateLeaveRequest, isUpdatingLeaveRequest } = useSchedules();
  const { user } = useAuth();
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [isWorkflowDialogOpen, setIsWorkflowDialogOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'approved': return <Check className="h-4 w-4" />;
      case 'rejected': return <X className="h-4 w-4" />;
      default: return null;
    }
  };

  const handleViewWorkflow = (request: LeaveRequest) => {
    setSelectedRequest(request);
    setIsWorkflowDialogOpen(true);
  };

  const handleApprove = (request: LeaveRequest) => {
    updateLeaveRequest({
      ...request,
      status: 'approved'
    });
  };

  const handleReject = (request: LeaveRequest) => {
    updateLeaveRequest({
      ...request,
      status: 'rejected'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return diffDays;
  };

  // Filter requests by status
  const pendingRequests = leaveRequests.filter(r => r.status === 'pending');
  const approvedRequests = leaveRequests.filter(r => r.status === 'approved');
  const rejectedRequests = leaveRequests.filter(r => r.status === 'rejected');

  const renderRequestCard = (request: LeaveRequest) => (
    <div key={request.id} className="border rounded-lg p-4 space-y-3">
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <h3 className="font-medium">
              {request.employee?.firstName} {request.employee?.lastName}
            </h3>
            <Badge className={getStatusColor(request.status)}>
              <span className="flex items-center space-x-1">
                {getStatusIcon(request.status)}
                <span className="capitalize">{request.status}</span>
              </span>
            </Badge>
          </div>
          <p className="text-sm text-gray-600">
            {request.employee?.department} - {request.employee?.position}
          </p>
          <p className="text-sm font-medium text-blue-600">
            {request.leaveType}
          </p>
        </div>

        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleViewWorkflow(request)}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
          {request.status === 'pending' && (
            <>
              <Button
                size="sm"
                variant="outline"
                className="text-green-600 border-green-600 hover:bg-green-50"
                onClick={() => handleApprove(request)}
                disabled={isUpdatingLeaveRequest}
              >
                <Check className="h-4 w-4 mr-1" />
                Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="text-red-600 border-red-600 hover:bg-red-50"
                onClick={() => handleReject(request)}
                disabled={isUpdatingLeaveRequest}
              >
                <X className="h-4 w-4 mr-1" />
                Reject
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div>
          <span className="text-gray-600">Start Date:</span>
          <p className="font-medium">{formatDate(request.startDate)}</p>
        </div>
        <div>
          <span className="text-gray-600">End Date:</span>
          <p className="font-medium">{formatDate(request.endDate)}</p>
        </div>
        <div>
          <span className="text-gray-600">Duration:</span>
          <p className="font-medium">{calculateDuration(request.startDate, request.endDate)} days</p>
        </div>
      </div>

      {request.reason && (
        <div className="text-sm">
          <span className="text-gray-600">Reason:</span>
          <p className="mt-1">{request.reason}</p>
        </div>
      )}

      <div className="text-xs text-gray-500">
        Submitted on {new Date(request.createdAt || '').toLocaleDateString('id-ID')}
      </div>
    </div>
  );

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Leave Requests Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="pending" className="space-y-4">
            <TabsList>
              <TabsTrigger value="pending" className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4" />
                <span>Pending ({pendingRequests.length})</span>
              </TabsTrigger>
              <TabsTrigger value="approved" className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>Approved ({approvedRequests.length})</span>
              </TabsTrigger>
              <TabsTrigger value="rejected" className="flex items-center space-x-2">
                <XCircle className="h-4 w-4" />
                <span>Rejected ({rejectedRequests.length})</span>
              </TabsTrigger>
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>All ({leaveRequests.length})</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pending">
              <div className="space-y-4">
                {pendingRequests.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No pending leave requests</p>
                  </div>
                ) : (
                  pendingRequests.map(renderRequestCard)
                )}
              </div>
            </TabsContent>

            <TabsContent value="approved">
              <div className="space-y-4">
                {approvedRequests.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No approved leave requests</p>
                  </div>
                ) : (
                  approvedRequests.map(renderRequestCard)
                )}
              </div>
            </TabsContent>

            <TabsContent value="rejected">
              <div className="space-y-4">
                {rejectedRequests.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <XCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No rejected leave requests</p>
                  </div>
                ) : (
                  rejectedRequests.map(renderRequestCard)
                )}
              </div>
            </TabsContent>

            <TabsContent value="all">
              <div className="space-y-4">
                {leaveRequests.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No leave requests found</p>
                  </div>
                ) : (
                  leaveRequests.map(renderRequestCard)
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Approval Workflow Dialog */}
      <Dialog open={isWorkflowDialogOpen} onOpenChange={setIsWorkflowDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Leave Request Approval Workflow</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <LeaveApprovalWorkflow
              leaveRequest={selectedRequest}
              onApprovalUpdate={() => {
                setIsWorkflowDialogOpen(false);
                // Refresh the data if needed
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}

