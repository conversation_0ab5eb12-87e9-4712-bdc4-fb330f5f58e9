
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Check, X, Clock } from 'lucide-react';
import { LeaveRequest } from '@/types/schedule';
import { useSchedules } from '@/hooks/useSchedules';

interface LeaveRequestsListProps {
  leaveRequests: LeaveRequest[];
}

export function LeaveRequestsList({ leaveRequests }: LeaveRequestsListProps) {
  const { updateLeaveRequest, isUpdatingLeaveRequest } = useSchedules();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'approved': return <Check className="h-4 w-4" />;
      case 'rejected': return <X className="h-4 w-4" />;
      default: return null;
    }
  };

  const handleApprove = (request: LeaveRequest) => {
    updateLeaveRequest({
      ...request,
      status: 'approved'
    });
  };

  const handleReject = (request: LeaveRequest) => {
    updateLeaveRequest({
      ...request,
      status: 'rejected'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return diffDays;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Permohonan Cuti</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {leaveRequests.map((request) => (
            <div key={request.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium">
                      {request.employee?.firstName} {request.employee?.lastName}
                    </h3>
                    <Badge className={getStatusColor(request.status)}>
                      <span className="flex items-center space-x-1">
                        {getStatusIcon(request.status)}
                        <span className="capitalize">{request.status}</span>
                      </span>
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    {request.employee?.department} - {request.employee?.position}
                  </p>
                  <p className="text-sm font-medium text-blue-600">
                    {request.leaveType}
                  </p>
                </div>
                
                {request.status === 'pending' && (
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-green-600 border-green-600 hover:bg-green-50"
                      onClick={() => handleApprove(request)}
                      disabled={isUpdatingLeaveRequest}
                    >
                      <Check className="h-4 w-4 mr-1" />
                      Setujui
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 border-red-600 hover:bg-red-50"
                      onClick={() => handleReject(request)}
                      disabled={isUpdatingLeaveRequest}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Tolak
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Periode Cuti:</p>
                  <p className="font-medium">
                    {formatDate(request.startDate)} - {formatDate(request.endDate)}
                  </p>
                  <p className="text-xs text-gray-500">
                    ({calculateDuration(request.startDate, request.endDate)} hari)
                  </p>
                </div>
                
                <div>
                  <p className="text-gray-600">Tanggal Pengajuan:</p>
                  <p className="font-medium">
                    {request.createdAt && formatDate(request.createdAt.split('T')[0])}
                  </p>
                </div>
              </div>
              
              {request.reason && (
                <div>
                  <p className="text-sm text-gray-600">Alasan:</p>
                  <p className="text-sm bg-gray-50 p-2 rounded">{request.reason}</p>
                </div>
              )}
            </div>
          ))}
          
          {leaveRequests.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Belum ada permohonan cuti</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
