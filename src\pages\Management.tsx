
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { RoleManagement } from '@/components/Management/RoleManagement';
import { DepartmentManagement } from '@/components/Management/DepartmentManagement';
import { PositionManagement } from '@/components/Management/PositionManagement';
import { Shield, Building, Briefcase } from 'lucide-react';

export default function Management() {
  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Manajemen Organisasi</h1>
        <p className="text-gray-600"><PERSON><PERSON><PERSON> role, departemen, dan posisi dalam organisasi</p>
      </div>

      <Tabs defaultValue="roles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="roles" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Role</span>
          </TabsTrigger>
          <TabsTrigger value="departments" className="flex items-center space-x-2">
            <Building className="h-4 w-4" />
            <span>Departemen</span>
          </TabsTrigger>
          <TabsTrigger value="positions" className="flex items-center space-x-2">
            <Briefcase className="h-4 w-4" />
            <span>Posisi</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles">
          <RoleManagement />
        </TabsContent>

        <TabsContent value="departments">
          <DepartmentManagement />
        </TabsContent>

        <TabsContent value="positions">
          <PositionManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
}
