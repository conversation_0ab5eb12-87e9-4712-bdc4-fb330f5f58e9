import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { LeaveBalance } from '@/types/schedule';
import { useAuth } from '@/hooks/useAuth';

interface LeaveBalanceCardProps {
  employeeId?: string;
  showAllEmployees?: boolean;
}

export function LeaveBalanceCard({ employeeId, showAllEmployees = false }: LeaveBalanceCardProps) {
  const [balances, setBalances] = useState<LeaveBalance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    loadLeaveBalances();
  }, [employeeId, selectedYear, showAllEmployees]);

  const loadLeaveBalances = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('leave_balances')
        .select(`
          *,
          employee:employees!employee_id (
            first_name,
            last_name,
            department,
            position,
            join_date
          )
        `)
        .eq('year', selectedYear);

      if (!showAllEmployees && employeeId) {
        query = query.eq('employee_id', employeeId);
      } else if (!showAllEmployees && user?.email) {
        // Get current user's employee ID
        const { data: currentEmployee } = await supabase
          .from('employees')
          .select('id')
          .eq('email', user.email)
          .single();
        
        if (currentEmployee) {
          query = query.eq('employee_id', currentEmployee.id);
        }
      }

      const { data, error } = await query.order('annual_leave_remaining', { ascending: false });

      if (error) throw error;
      setBalances(data || []);
    } catch (error) {
      console.error('Error loading leave balances:', error);
      toast({
        title: "Error",
        description: "Failed to load leave balances",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const calculateLeaveUsagePercentage = (used: number, total: number) => {
    return total > 0 ? (used / total) * 100 : 0;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const exportBalanceReport = async () => {
    try {
      // Create CSV content
      const headers = [
        'Employee Name',
        'Department',
        'Position',
        'Year',
        'Annual Leave Total',
        'Annual Leave Used',
        'Annual Leave Remaining',
        'Sick Leave Used',
        'Emergency Leave Used',
        'Maternity Leave Used',
        'Usage Percentage'
      ];

      const csvContent = [
        headers.join(','),
        ...balances.map(balance => [
          `"${balance.employee?.firstName} ${balance.employee?.lastName}"`,
          balance.employee?.department,
          balance.employee?.position,
          balance.year,
          balance.annualLeaveTotal,
          balance.annualLeaveUsed,
          balance.annualLeaveRemaining,
          balance.sickLeaveUsed,
          balance.emergencyLeaveUsed,
          balance.maternityLeaveUsed,
          `${calculateLeaveUsagePercentage(balance.annualLeaveUsed, balance.annualLeaveTotal).toFixed(1)}%`
        ].join(','))
      ].join('\n');

      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leave_balances_${selectedYear}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Leave balance report exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export report",
        variant: "destructive",
      });
    }
  };

  const renderBalanceCard = (balance: LeaveBalance) => {
    const usagePercentage = calculateLeaveUsagePercentage(balance.annualLeaveUsed, balance.annualLeaveTotal);
    const isLowBalance = balance.annualLeaveRemaining <= 2;
    const isHighUsage = usagePercentage >= 80;

    return (
      <Card key={balance.id} className={`${isLowBalance ? 'border-red-200 bg-red-50' : ''}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              {showAllEmployees && (
                <CardTitle className="text-lg">
                  {balance.employee?.firstName} {balance.employee?.lastName}
                </CardTitle>
              )}
              {showAllEmployees && (
                <p className="text-sm text-gray-600">
                  {balance.employee?.department} - {balance.employee?.position}
                </p>
              )}
              {!showAllEmployees && (
                <CardTitle className="text-lg">Leave Balance {balance.year}</CardTitle>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {isLowBalance && (
                <Badge variant="destructive" className="flex items-center space-x-1">
                  <AlertTriangle className="h-3 w-3" />
                  <span>Low Balance</span>
                </Badge>
              )}
              {isHighUsage && !isLowBalance && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <Clock className="h-3 w-3" />
                  <span>High Usage</span>
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Annual Leave Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Annual Leave</span>
              <span className={`text-sm font-bold ${getUsageColor(usagePercentage)}`}>
                {balance.annualLeaveRemaining} / {balance.annualLeaveTotal} days
              </span>
            </div>
            <Progress 
              value={usagePercentage} 
              className="h-2"
              style={{
                background: `linear-gradient(to right, ${getProgressColor(usagePercentage)} ${usagePercentage}%, #e5e7eb ${usagePercentage}%)`
              }}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Used: {balance.annualLeaveUsed} days</span>
              <span>{usagePercentage.toFixed(1)}% used</span>
            </div>
          </div>

          {/* Other Leave Types */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{balance.sickLeaveUsed}</div>
              <div className="text-xs text-gray-600">Sick Leave Used</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{balance.emergencyLeaveUsed}</div>
              <div className="text-xs text-gray-600">Emergency Leave</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{balance.maternityLeaveUsed}</div>
              <div className="text-xs text-gray-600">Maternity Leave</div>
            </div>
          </div>

          {/* Recommendations */}
          {isLowBalance && (
            <div className="p-3 bg-red-100 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">Low Balance Alert</span>
              </div>
              <p className="text-xs text-red-700 mt-1">
                Consider planning your remaining leave days carefully. Only {balance.annualLeaveRemaining} days remaining.
              </p>
            </div>
          )}

          {balance.annualLeaveRemaining > 5 && usagePercentage < 50 && (
            <div className="p-3 bg-green-100 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Good Balance</span>
              </div>
              <p className="text-xs text-green-700 mt-1">
                You have a healthy leave balance. Consider taking some time off for rest and relaxation.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const years = [selectedYear - 1, selectedYear, selectedYear + 1];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Leave Balance Tracking</h2>
          <p className="text-gray-600">Monitor annual leave usage and balances</p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            {years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
          {showAllEmployees && (
            <Button onClick={exportBalanceReport} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
        </div>
      </div>

      {/* Summary Statistics for All Employees View */}
      {showAllEmployees && balances.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{balances.length}</div>
              <div className="text-sm text-gray-600">Total Employees</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {balances.reduce((sum, b) => sum + b.annualLeaveRemaining, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Days Remaining</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">
                {balances.reduce((sum, b) => sum + b.annualLeaveUsed, 0)}
              </div>
              <div className="text-sm text-gray-600">Total Days Used</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">
                {balances.filter(b => b.annualLeaveRemaining <= 2).length}
              </div>
              <div className="text-sm text-gray-600">Low Balance Alerts</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Balance Cards */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Clock className="h-8 w-8 animate-spin" />
          </div>
        ) : balances.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-gray-500">No leave balance data found for {selectedYear}</p>
            </CardContent>
          </Card>
        ) : (
          <div className={showAllEmployees ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : ""}>
            {balances.map(renderBalanceCard)}
          </div>
        )}
      </div>
    </div>
  );
}
